import { createServerClient } from '@supabase/ssr';
import createMiddleware from 'next-intl/middleware';
import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import { defaultLocale, locales } from './i18n/config';
import {
  createSafeRedirectUrl,
  fetchUserProfile,
  getUserDataFromCookies,
  hasRoutePermission,
  isAuthPath,
  isOnboardingPath,
  isProtectedPath,
  setUserCookies,
} from './lib/middleware-utils';
import type { UserRole } from './types/auth';

// Create the i18n middleware
const intlMiddleware = createMiddleware({
  locales,
  defaultLocale,
  localePrefix: 'always',
});

export async function middleware(req: NextRequest) {
  // Handle i18n routing first
  const pathname = req.nextUrl.pathname;

  // Skip locale handling for API routes and auth callbacks
  if (pathname.startsWith('/api/') || pathname.startsWith('/auth/')) {
    return NextResponse.next();
  }

  // Check if the request is for a locale-specific path
  const pathnameIsMissingLocale = locales.every(
    (locale) =>
      !pathname.startsWith(`/${locale}/`) && pathname !== `/${locale}`,
  );

  // Redirect if there is no locale (except for API routes)
  if (pathnameIsMissingLocale) {
    return intlMiddleware(req);
  }

  // Extract locale from pathname
  const locale = pathname.split('/')[1];
  const pathnameWithoutLocale = pathname.replace(`/${locale}`, '') || '/';

  // Initialize Supabase client
  let supabaseResponse = NextResponse.next({ request: req });
  const cookies = req.cookies.getAll();

  // Extract Supabase auth tokens from cookies
  const accessTokenCookie = cookies.find((c) => c.name === 'sb-access-token');
  const refreshTokenCookie = cookies.find((c) => c.name === 'sb-refresh-token');

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return req.cookies.getAll();
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value, options: _options }) =>
            req.cookies.set(name, value),
          );
          supabaseResponse = NextResponse.next({ request: req });
          cookiesToSet.forEach(({ name, value, options }) =>
            supabaseResponse.cookies.set(name, value, options),
          );
        },
      },
    },
  );

  // If tokens are available, set them explicitly in the Supabase client
  if (accessTokenCookie && refreshTokenCookie) {
    await supabase.auth.setSession({
      access_token: accessTokenCookie.value,
      refresh_token: refreshTokenCookie.value,
    });
  }

  // Get session and ensure cookies are up-to-date
  const { data } = await supabase.auth.getSession();
  let currentSession = data.session;

  // If session is null but there might be a recent login, try to refresh
  if (!currentSession) {
    const { data: refreshedSession } = await supabase.auth.refreshSession();
    if (refreshedSession?.session) {
      currentSession = refreshedSession.session;
      // Update cookies with the refreshed session
      supabaseResponse.cookies.set(
        'sb-access-token',
        currentSession.access_token,
        { path: '/', maxAge: 60 * 60 * 24 * 7 },
      );
      supabaseResponse.cookies.set(
        'sb-refresh-token',
        currentSession.refresh_token,
        { path: '/', maxAge: 60 * 60 * 24 * 7 },
      );
    }
  }

  const isProtected = isProtectedPath(pathnameWithoutLocale);
  const isAuth = isAuthPath(pathnameWithoutLocale);

  // Handle unauthenticated access to protected routes
  if (isProtected && !currentSession) {
    return NextResponse.redirect(new URL('/', req.url));
  }

  // Handle authenticated users on protected routes
  if (isProtected && currentSession) {
    const redirectResponse = await handleProtectedRoute(
      req,
      supabase,
      currentSession,
      supabaseResponse,
      locale,
    );
    if (redirectResponse) return redirectResponse;
  } // Handle authenticated users on auth routes
  if (isAuth && currentSession) {
    const redirectTo = req.nextUrl.searchParams.get('redirectTo');

    // Get user role to determine appropriate default redirect
    let defaultPath = '/projects'; // Default for contractors and other users
    const { userRole } = getUserDataFromCookies(req);

    if (userRole === 'admin') {
      defaultPath = '/dashboard'; // Admin users go to admin dashboard
    } else if (userRole === 'viewer') {
      defaultPath = '/daily-logs'; // Viewer users should go to daily logs by default
    }

    const redirectUrl = createSafeRedirectUrl(
      redirectTo,
      `/${locale}${defaultPath}`,
      req.url,
    );
    return NextResponse.redirect(new URL(redirectUrl, req.url));
  }

  return supabaseResponse;
}

/**
 * Handle authenticated users accessing protected routes
 */
async function handleProtectedRoute(
  req: NextRequest,
  supabase: ReturnType<typeof createServerClient>,
  session: { user: { id: string; user_metadata?: Record<string, unknown> } },
  supabaseResponse: NextResponse,
  locale: string,
): Promise<NextResponse | null> {
  try {
    // Get user data from cookies or database
    let { userRole, onboardingCompleted } = getUserDataFromCookies(req);

    // Fetch from database if not in cookies or if onboarding appears incomplete
    if (
      !userRole ||
      req.cookies.get('onboarding_completed')?.value === undefined ||
      onboardingCompleted === false
    ) {
      const profileData = await fetchUserProfile(supabase, session.user.id);

      if (profileData) {
        userRole = profileData.userRole;
        onboardingCompleted = profileData.onboardingCompleted;
      } else {
        // Fallback to session metadata if database query fails
        userRole = session.user.user_metadata?.role as UserRole;
        onboardingCompleted = true; // Assume completed if we can't fetch from DB
      }

      if (userRole) {
        // Store in cookies for future requests
        setUserCookies(supabaseResponse, userRole, onboardingCompleted);
      }
    }

    // Check onboarding completion
    const onboardingRedirect = handleOnboardingCheck(
      req,
      onboardingCompleted,
      locale,
    );
    if (onboardingRedirect) {
      return onboardingRedirect;
    }

    // Check role-based permissions
    const rbacRedirect = handleRBACCheck(req, userRole, locale);
    if (rbacRedirect) {
      return rbacRedirect;
    }

    return null;
  } catch (error) {
    console.error('Protected route middleware error:', error);
    return null;
  }
}

/**
 * Handle onboarding completion check
 */
function handleOnboardingCheck(
  req: NextRequest,
  onboardingCompleted: boolean,
  locale: string,
): NextResponse | null {
  const pathnameWithoutLocale =
    req.nextUrl.pathname.replace(`/${locale}`, '') || '/';

  if (!onboardingCompleted && !isOnboardingPath(pathnameWithoutLocale)) {
    return NextResponse.redirect(new URL(`/${locale}/profile`, req.url));
  }

  return null;
}

/**
 * Handle role-based access control
 */
function handleRBACCheck(
  req: NextRequest,
  userRole: UserRole | undefined,
  locale: string,
): NextResponse | null {
  if (!userRole) {
    return null;
  }

  const pathnameWithoutLocale =
    req.nextUrl.pathname.replace(`/${locale}`, '') || '/';

  const hasPermission = hasRoutePermission(pathnameWithoutLocale, userRole);

  if (!hasPermission) {
    // Redirect to appropriate default page based on user role
    let defaultPath = '/projects';
    if (userRole === 'viewer') {
      defaultPath = '/daily-logs'; // Viewer users can only access daily logs and complaints
    }

    const accessDeniedUrl = new URL(`/${locale}${defaultPath}`, req.url);
    accessDeniedUrl.searchParams.set('error', 'access_denied');
    return NextResponse.redirect(accessDeniedUrl);
  }

  return null;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public files (images, etc.)
     * Note: This needs to be last to avoid overriding other matchers
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
};
