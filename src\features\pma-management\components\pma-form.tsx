'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { FileUploadDropzone } from '@/components/ui/file-upload-dropzone';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { useCurrentUserCompetentPersons } from '@/hooks/use-profile';
import { usePmaManagementTranslations } from '@/hooks/use-translations';
import { zodResolver } from '@hookform/resolvers/zod';
import { Calendar, MapPin, Plus, Trash2, Upload, User } from 'lucide-react';
import { useFieldArray, useForm } from 'react-hook-form';
import * as z from 'zod';
import { pmaFormSchema } from '../schemas/pma-schema';

const pmasListSchema = z.object({
  pmas: z.array(pmaFormSchema).min(1, 'At least one PMA is required.'),
});

type PmasListSchema = z.infer<typeof pmasListSchema>;

interface PmaFormProps {
  onSubmit: (data: PmasListSchema) => void;
  initialData?: Partial<PmasListSchema>;
  isLoading?: boolean;
  isEditMode?: boolean;
}

export function PmaForm({
  onSubmit,
  initialData,
  isLoading,
  isEditMode = false,
}: PmaFormProps) {
  const t = usePmaManagementTranslations();

  // Get competent persons for the current user's contractor
  const { data: competentPersons, isLoading: loadingCompetentPersons } =
    useCurrentUserCompetentPersons();

  const form = useForm<PmasListSchema>({
    resolver: zodResolver(pmasListSchema),
    defaultValues: initialData || {
      pmas: [
        {
          location: '',
          pmaNumber: '',
          dateReceived: '',
          competentPersonId: '',
          pmaExpiryDate: '',
          pdfFile: undefined,
        },
      ],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'pmas',
  });

  const handleSubmit = (data: PmasListSchema) => {
    onSubmit(data);
  };

  const addPma = () => {
    append({
      location: '',
      pmaNumber: '',
      dateReceived: '',
      competentPersonId: '',
      pmaExpiryDate: '',
      pdfFile: undefined,
    });
  };

  const removePma = (index: number) => {
    remove(index);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-7">
        <div className="space-y-6">
          {fields.map((field, index) => (
            <div
              key={field.id}
              className="relative bg-white/60 backdrop-blur-sm rounded-xl border border-slate-200/60 p-6 shadow-xl shadow-slate-200/20 space-y-6"
            >
              <div className="flex items-center justify-between pb-4 border-b border-slate-200/80">
                <h3 className="text-lg font-semibold flex items-center gap-2 text-slate-800">
                  <div className="w-2 h-2 bg-primary rounded-full"></div>
                  {t('form.pmaEntry', { number: index + 1 })}
                </h3>
                {fields.length > 1 && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => removePma(index)}
                    className="text-destructive hover:text-destructive hover:bg-destructive/10"
                  >
                    <Trash2 className="h-4 w-4 mr-1" />
                    {t('form.remove')}
                  </Button>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* PMA Number */}
                <FormField
                  control={form.control}
                  name={`pmas.${index}.pmaNumber`}
                  render={({ field: formField }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        <div className="h-4 w-4 text-primary">#</div>
                        {t('form.pmaNumber')}
                        <span className="text-destructive">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder={t('form.pmaNumberPlaceholder')}
                          {...formField}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Competent Person */}
                <FormField
                  control={form.control}
                  name={`pmas.${index}.competentPersonId`}
                  render={({ field: formField }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        <User className="h-4 w-4 text-primary" />
                        {t('form.competentPerson')}
                        <span className="text-destructive">*</span>
                      </FormLabel>
                      <Select
                        onValueChange={formField.onChange}
                        value={formField.value}
                        disabled={
                          loadingCompetentPersons || !competentPersons?.length
                        }
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue
                              placeholder={
                                loadingCompetentPersons
                                  ? 'Loading competent persons...'
                                  : !competentPersons?.length
                                    ? t('form.noCompetentPersonsAvailable')
                                    : t('form.competentPersonPlaceholder')
                              }
                            />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {competentPersons?.map((cp) => (
                            <SelectItem key={cp.id} value={cp.id}>
                              <div className="flex flex-col items-start">
                                <span className="font-medium text-left">
                                  {cp.name}
                                </span>
                                <span className="text-xs text-muted-foreground text-left">
                                  {cp.cp_type} •{' '}
                                  {cp.cp_registeration_no ||
                                    'No registration number'}
                                </span>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                      {!loadingCompetentPersons &&
                        !competentPersons?.length && (
                          <p className="text-xs text-muted-foreground">
                            No competent persons found. Please add competent
                            persons to your contractor profile first.
                          </p>
                        )}
                    </FormItem>
                  )}
                />

                {/* Date Received */}
                <FormField
                  control={form.control}
                  name={`pmas.${index}.dateReceived`}
                  render={({ field: formField }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-primary" />
                        {t('form.dateReceived')}
                        <span className="text-destructive">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input type="date" {...formField} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* PMA Expiry Date */}
                <FormField
                  control={form.control}
                  name={`pmas.${index}.pmaExpiryDate`}
                  render={({ field: formField }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-primary" />
                        {t('form.pmaExpiryDate')}
                        <span className="text-destructive">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input type="date" {...formField} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* LIF Location - Full Width */}
              <FormField
                control={form.control}
                name={`pmas.${index}.location`}
                render={({ field: formField }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <MapPin className="h-4 w-4 text-primary" />
                      {t('form.lifLocation')}
                      <span className="text-destructive">*</span>
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder={t('form.lifLocationPlaceholder')}
                        className="min-h-[90px] resize-none"
                        {...formField}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* PDF Upload */}
              <FormField
                control={form.control}
                name={`pmas.${index}.pdfFile`}
                render={({ field: formField }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <Upload className="h-4 w-4 text-primary" />
                      PMA Certificate File
                      <span className="text-destructive">*</span>
                      <span className="text-xs text-muted-foreground font-normal">
                        (PDF only, max 10MB)
                      </span>
                    </FormLabel>
                    <FormControl>
                      <FileUploadDropzone
                        onFilesChange={(files) => {
                          formField.onChange(files[0] || undefined);
                        }}
                        accept=".pdf"
                        maxSize={10 * 1024 * 1024} // 10MB
                        maxFiles={1}
                        files={formField.value ? [formField.value] : []}
                        className="border-2 border-dashed border-slate-300 rounded-lg p-8 text-center hover:border-primary/50 transition-colors"
                      />
                    </FormControl>
                    <FormMessage />
                    <p className="text-sm text-muted-foreground">
                      {t('form.pdfUploadDescription')}
                    </p>
                  </FormItem>
                )}
              />
            </div>
          ))}
          {form.formState.errors.pmas &&
            typeof form.formState.errors.pmas === 'object' &&
            'message' in form.formState.errors.pmas && (
              <p className="text-sm font-medium text-destructive">
                {form.formState.errors.pmas.message}
              </p>
            )}
        </div>

        {!isEditMode && (
          <Button
            type="button"
            variant="outline"
            onClick={addPma}
            className="w-full md:w-auto"
          >
            <Plus className="h-4 w-4 mr-2" />
            {t('actions.addAnotherPma')}
          </Button>
        )}

        {/* Submit Button */}
        <div className="flex justify-end pt-4">
          <Button
            type="submit"
            size="lg"
            disabled={isLoading}
            className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white hover:from-blue-600 hover:to-indigo-700"
          >
            {isLoading ? (
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white" />
            ) : isEditMode ? (
              t('actions.saveChanges')
            ) : (
              t('actions.submitPma')
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}
