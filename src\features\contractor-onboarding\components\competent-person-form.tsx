'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  FileUploadModal,
  type FileUploadResult,
} from '@/components/ui/file-upload-modal';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { useContractorTranslations } from '@/hooks/use-translations';
import { ExternalLink, FileText, Trash2, Upload, X } from 'lucide-react';
import React, { useState } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { type ContractorStep3FormValues } from '../schemas/contractor-onboarding-schemas';

interface CompetentPersonFormProps {
  form: UseFormReturn<ContractorStep3FormValues>;
  index: number;
  onRemove: (index: number) => void;
  canRemove: boolean;
}

export const CompetentPersonForm = React.memo<CompetentPersonFormProps>(
  ({ form, index, onRemove, canRemove }) => {
    const t = useContractorTranslations();
    const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);

    const handleCertificateUpload = async (results: FileUploadResult[]) => {
      // Use the actual uploaded URL from the upload result
      if (results.length > 0) {
        const uploadedUrl = results[0].url;
        form.setValue(
          `competent_persons.${index}.cp_registeration_cert`,
          uploadedUrl,
        );
      }
      setIsUploadModalOpen(false);
    };

    // Helper function to extract filename from URL
    const getFilenameFromUrl = (url: string): string => {
      if (!url) return '';
      try {
        const urlObj = new URL(url);
        const pathname = urlObj.pathname;
        const filename = pathname.split('/').pop() || '';
        return decodeURIComponent(filename);
      } catch {
        // If URL parsing fails, try to extract from the path
        const parts = url.split('/');
        return parts[parts.length - 1] || 'certificate.pdf';
      }
    };

    // Helper function to remove the certificate
    const handleRemoveCertificate = () => {
      form.setValue(`competent_persons.${index}.cp_registeration_cert`, '');
    };

    return (
      <>
        <div className="border rounded-lg p-6 space-y-4 bg-muted/30">
          <div className="flex justify-between items-center">
            <h4 className="font-medium text-lg">
              {t('onboarding.step4.competentPersons.title')} {index + 1}
            </h4>
            {canRemove && (
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => onRemove(index)}
                className="text-destructive hover:text-destructive"
              >
                <Trash2 className="w-4 h-4 mr-1" />
                {t('onboarding.step4.competentPersons.removeButton')}
              </Button>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name={`competent_persons.${index}.name`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {t('onboarding.step4.competentPersons.name')}
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder={t(
                        'onboarding.step4.competentPersons.namePlaceholder',
                      )}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name={`competent_persons.${index}.ic_no`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {t('onboarding.step4.competentPersons.icNo')}
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder={t(
                        'onboarding.step4.competentPersons.icNoPlaceholder',
                      )}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name={`competent_persons.${index}.phone_no`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {t('onboarding.step4.competentPersons.phoneNo')}
                  </FormLabel>
                  <FormControl>
                    <Input
                      type="tel"
                      placeholder={t(
                        'onboarding.step4.competentPersons.phoneNoPlaceholder',
                      )}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name={`competent_persons.${index}.cp_type`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {t('onboarding.step4.competentPersons.cpType')}
                  </FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue
                          placeholder={t(
                            'onboarding.step4.competentPersons.cpTypePlaceholder',
                          )}
                        />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="CP1">
                        {t('onboarding.step4.competentPersons.types.CP1')}
                      </SelectItem>
                      <SelectItem value="CP2">
                        {t('onboarding.step4.competentPersons.types.CP2')}
                      </SelectItem>
                      <SelectItem value="CP3">
                        {t('onboarding.step4.competentPersons.types.CP3')}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name={`competent_persons.${index}.cp_registeration_no`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {t('onboarding.step4.competentPersons.cpRegisterationNo')}
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder={t(
                        'onboarding.step4.competentPersons.cpRegisterationNoPlaceholder',
                      )}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name={`competent_persons.${index}.cert_exp_date`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {t('onboarding.step4.competentPersons.certExpDate')}
                  </FormLabel>
                  <FormControl>
                    <Input type="date" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name={`competent_persons.${index}.no_of_pma`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {t('onboarding.step4.competentPersons.noOfPma')}
                  </FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      min="0"
                      {...field}
                      onChange={(e) =>
                        field.onChange(parseInt(e.target.value) || 0)
                      }
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name={`competent_persons.${index}.cp_registeration_cert`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {t('onboarding.step4.competentPersons.cpRegisterationCert')}
                  </FormLabel>
                  <FormControl>
                    <div className="space-y-3">
                      {field.value ? (
                        // Certificate Preview Card
                        <div
                          className="relative group border rounded-lg p-4 bg-muted/30 hover:bg-muted/50 transition-colors cursor-pointer"
                          onClick={() => window.open(field.value, '_blank')}
                        >
                          <div className="flex items-center space-x-3">
                            <div className="flex-shrink-0">
                              <FileText className="w-8 h-8 text-red-600" />
                            </div>
                            <div className="flex-1 min-w-0">
                              <p className="text-sm font-medium text-foreground truncate max-w-[200px] overflow-hidden text-ellipsis whitespace-nowrap">
                                {getFilenameFromUrl(field.value)}
                              </p>
                              <p className="text-xs text-muted-foreground">
                                {t(
                                  'onboarding.step4.competentPersons.previewCert',
                                )}
                              </p>
                            </div>
                            <div className="flex items-center space-x-2">
                              <ExternalLink className="w-4 h-4 text-muted-foreground group-hover:text-primary" />
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleRemoveCertificate();
                                }}
                                className="h-8 w-8 p-0 hover:bg-destructive/10"
                              >
                                <X className="w-4 h-4 text-destructive" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      ) : (
                        // Upload Button
                        <Button
                          type="button"
                          variant="outline"
                          className="w-full h-20 border-dashed"
                          onClick={() => setIsUploadModalOpen(true)}
                        >
                          <div className="flex flex-col items-center space-y-2">
                            <Upload className="w-6 h-6 text-muted-foreground" />
                            <span className="text-sm">
                              {t(
                                'onboarding.step4.competentPersons.uploadCert',
                              )}
                            </span>
                          </div>
                        </Button>
                      )}
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name={`competent_persons.${index}.address`}
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  {t('onboarding.step4.competentPersons.address')}
                </FormLabel>
                <FormControl>
                  <Textarea
                    placeholder={t(
                      'onboarding.step4.competentPersons.addressPlaceholder',
                    )}
                    rows={3}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Certificate Upload Modal */}
        <FileUploadModal
          isOpen={isUploadModalOpen}
          onClose={() => setIsUploadModalOpen(false)}
          onUpload={handleCertificateUpload}
          title={`${t('onboarding.step4.competentPersons.uploadCert')} - ${t('onboarding.step4.competentPersons.title')} ${index + 1}`}
          description="Upload competent person registration certificate. Accepted formats: PDF, JPG, PNG (max 10MB)"
          maxFiles={1}
          acceptedTypes=".pdf,.jpg,.jpeg,.png"
          maxSize={10 * 1024 * 1024}
          folderPath={`competent-persons/certificates`}
        />
      </>
    );
  },
);

CompetentPersonForm.displayName = 'CompetentPersonForm';
