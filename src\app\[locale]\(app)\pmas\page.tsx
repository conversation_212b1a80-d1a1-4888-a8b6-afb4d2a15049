'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { PmaFilterSection } from '@/features/pma-management/components/PmaFilterSection';
import PmaTableSection from '@/features/pma-management/components/PmaTableSection';
import { StatusCards } from '@/features/pma-management/components/StatusCards';
import {
  usePMACertificates,
  usePMAStats,
} from '@/features/pma-management/hooks/use-pma-certificates';
import type { PmaDbStatus } from '@/features/pma-management/types/pma-certificate';
import { usePagination } from '@/hooks/use-pagination';
import { useProjectContext } from '@/providers/project-context';
import { Plus } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useParams, useRouter } from 'next/navigation';
import { useCallback, useEffect, useMemo, useState } from 'react';

// Define the shape of the table state
interface TableState {
  sorting: {
    column: string;
    direction: 'asc' | 'desc';
  };
  columnVisibility: Record<string, boolean>;
}

export default function PmaManagement() {
  const router = useRouter();
  const params = useParams();
  const locale = (params?.locale as string) || 'en';
  const t = useTranslations('pages.maintenanceLogs.page');
  const tPma = useTranslations('pmaManagement');

  // Get current projectId from context
  const { selectedProjectId } = useProjectContext();

  // Initial totalRecords for pagination setup
  const initialTotalRecords = 0;

  // Pagination state (replaced with usePagination)
  const {
    currentPage,
    pageSize: rowsPerPage,
    totalPages,
    setCurrentPage,
    setPageSize: setRowsPerPage,
    updateTotalRecords,
  } = usePagination({ totalRecords: initialTotalRecords });

  // Define columns for the PMA table
  const columns: { key: string; label: string }[] = useMemo(
    () => [
      { key: 'pma_number', label: tPma('table.pmaNumber') },
      { key: 'location', label: tPma('form.location') },
      { key: 'status', label: tPma('table.status') },
      { key: 'expiry_date', label: tPma('table.expiryDate') },
      { key: 'total_repair_cost', label: tPma('table.totalRepairCost') },
      { key: 'total_repair_time', label: tPma('table.totalRepairTime') },
      { key: 'actions', label: tPma('table.actions') },
    ],
    [tPma],
  );

  // Table state management
  const [tableState, setTableState] = useState<TableState>({
    sorting: { column: 'created_at', direction: 'desc' },
    columnVisibility: columns.reduce(
      (acc, col) => ({ ...acc, [col.key]: true }),
      {},
    ),
  });
  const [filters, setFilters] = useState<{
    search: string;
    status?: PmaDbStatus | 'all';
  }>({
    search: '',
  });

  // Handler to update table state
  const handleTableStateChange = useCallback(
    (newState: Partial<TableState>) => {
      setTableState((prev) => ({ ...prev, ...newState }));
    },
    [],
  );

  // Fetch paginated PMA certificates
  const {
    data: pmaResult,
    isLoading: isPmaLoading,
    error: pmaError,
  } = usePMACertificates(
    selectedProjectId,
    currentPage,
    rowsPerPage,
    tableState.sorting,
    filters.search,
    { status: filters.status },
  );

  // Debug logging for search functionality
  useEffect(() => {
    console.log('Search filters updated:', {
      search: filters.search,
      status: filters.status,
      projectId: selectedProjectId,
    });
  }, [filters.search, filters.status, selectedProjectId]);

  const paginatedData = pmaResult?.data ?? [];

  // Update pagination total records when PMA data changes
  useEffect(() => {
    if (pmaResult?.total !== undefined) {
      console.log('PMA Data Updated:', {
        total: pmaResult.total,
        dataCount: pmaResult.data?.length,
        currentPage,
        pageSize: rowsPerPage,
        projectId: selectedProjectId,
      });
      updateTotalRecords(pmaResult.total);
    }
  }, [
    pmaResult?.total,
    updateTotalRecords,
    pmaResult?.data?.length,
    currentPage,
    rowsPerPage,
    selectedProjectId,
  ]);

  // Reset to first page if data changes and currentPage is out of range
  useEffect(() => {
    if (currentPage > totalPages) {
      setCurrentPage(1);
    }
  }, [currentPage, setCurrentPage, totalPages]);

  // Fetch PMA stats from backend
  const { data: pmaStats } = usePMAStats(selectedProjectId);

  // Debug logging for PMA stats
  useEffect(() => {
    if (pmaStats) {
      console.log('PMA Stats Updated:', {
        active: pmaStats.active,
        expiringSoon: pmaStats.expiringSoon,
        expired: pmaStats.expired,
        total: pmaStats.active + pmaStats.expiringSoon + pmaStats.expired,
        projectId: selectedProjectId,
      });
    }
  }, [pmaStats, selectedProjectId]);

  // Handler for navigation (still useful for future real data)
  const handleAddNewPma = () => {
    router.push(`/${locale}/pmas/add`);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50/50 via-white to-blue-50/30">
      <main className="container mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8 lg:py-12">
        {/* Hero Section */}
        <div className="mb-8 sm:mb-12">
          <div className="text-center sm:text-left">
            <h1 className="text-base sm:text-2xl lg:text-4xl font-bold text-gray-900 tracking-tight">
              {tPma('activityLogs.title')}
            </h1>
            <p className="text-gray-600 mt-2 sm:mt-3 text-sm sm:text-base lg:text-lg max-w-2xl mx-auto sm:mx-0">
              {tPma('activityLogs.subtitle')}
            </p>
          </div>
        </div>

        {/* Status Cards */}
        <StatusCards
          activeCount={pmaStats?.active ?? 0}
          expirySoonCount={pmaStats?.expiringSoon ?? 0}
          expiredCount={pmaStats?.expired ?? 0}
          tPma={tPma}
        />

        {/* Section Header */}
        <section className="space-y-6 sm:space-y-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h2 className="text-base sm:text-2xl font-semibold text-gray-900">
                {tPma('table.pmaNumber')}
              </h2>
              <p className="text-sm sm:text-base text-gray-600 mt-1">
                {(pmaStats?.active ?? 0) +
                  (pmaStats?.expiringSoon ?? 0) +
                  (pmaStats?.expired ?? 0) >
                0
                  ? t('recordsFound', {
                      count:
                        (pmaStats?.active ?? 0) +
                        (pmaStats?.expiringSoon ?? 0) +
                        (pmaStats?.expired ?? 0),
                    })
                  : t('noRecords')}
              </p>
            </div>
            {/* Add Button */}
            <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 w-full sm:w-auto">
              <Button
                className="inline-flex items-center justify-center w-full sm:w-auto px-4 py-2.5 text-sm font-medium text-primary-foreground bg-primary rounded-xl hover:bg-primary/90 transition-all duration-200 shadow-sm hover:shadow-lg"
                onClick={handleAddNewPma}
              >
                <Plus className="w-4 h-4 mr-2" />
                {tPma('actions.addAnotherPma')}
              </Button>
              {/* <Link href={`/${locale}/pmas/trash`}>
                <Button
                  variant="outline"
                  className="inline-flex items-center justify-center w-full sm:w-auto px-4 py-2.5 text-sm font-medium border-gray-300 hover:border-gray-400 rounded-xl transition-all duration-200 shadow-sm hover:shadow-lg"
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  Trash
                </Button>
              </Link> */}
            </div>
          </div>

          <div className="bg-white/80 backdrop-blur-sm rounded-2xl border border-gray-200/60 shadow-sm hover:shadow-md transition-all duration-200">
            <div className="p-6 sm:p-8">
              <div className="flex items-center gap-3 mb-6">
                <div className="p-2.5 bg-gray-100 rounded-xl">
                  <svg
                    className="w-5 h-5 text-gray-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.207A1 1 0 013 6.5V4z"
                    />
                  </svg>
                </div>
                <div>
                  <h3 className="text-base sm:text-lg font-semibold text-gray-900">
                    {tPma('searchFilterTitle')}
                  </h3>
                  <p className="text-xs sm:text-sm text-gray-500 mt-0.5">
                    {tPma('searchFilterDescription')}
                  </p>
                </div>
              </div>
              <PmaFilterSection
                filters={filters}
                onFilterChange={setFilters}
                columns={columns}
                tableState={tableState}
                onTableStateChange={handleTableStateChange}
              />
            </div>
          </div>

          {/* PMA Entries Overview */}
          <Card className="w-full">
            {/* Table Header */}
            <div className="px-4 sm:px-8 py-4 sm:py-6 border-b border-gray-200/60 bg-gradient-to-r from-gray-50/50 to-gray-100/30">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                <div className="flex items-center gap-3">
                  <div className="p-2.5 bg-white rounded-xl shadow-sm ring-1 ring-gray-200/50">
                    <svg
                      className="w-5 h-5 text-gray-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2h2a2 2 0 002-2z"
                      />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-base sm:text-lg font-semibold text-gray-900">
                      {t('tableTitle')}
                    </h3>
                    <p className="text-xs sm:text-sm text-gray-500 mt-0.5">
                      {(pmaStats?.active ?? 0) +
                        (pmaStats?.expiringSoon ?? 0) +
                        (pmaStats?.expired ?? 0) >
                      0
                        ? t('recordsFound', {
                            count:
                              (pmaStats?.active ?? 0) +
                              (pmaStats?.expiringSoon ?? 0) +
                              (pmaStats?.expired ?? 0),
                          })
                        : t('noData')}
                    </p>
                  </div>
                </div>
                {(pmaStats?.active ?? 0) +
                  (pmaStats?.expiringSoon ?? 0) +
                  (pmaStats?.expired ?? 0) >
                  0 && (
                  <div className="flex items-center gap-2 px-3 py-1.5 bg-white rounded-lg shadow-sm ring-1 ring-gray-200/50">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    <span className="text-xs sm:text-sm text-gray-600 font-medium">
                      {t('pageOf', { current: currentPage, total: totalPages })}
                    </span>
                  </div>
                )}
              </div>
            </div>
            <div className="overflow-x-auto w-full">
              <PmaTableSection
                data={paginatedData}
                columns={columns}
                isLoading={isPmaLoading}
                emptyMessage={pmaError ? t('errorTitle') : t('noData')}
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={setCurrentPage}
                pageSize={rowsPerPage}
                onPageSizeChange={setRowsPerPage}
                // totalRecords={pmaResult?.total ?? 0}
                tPma={tPma}
                tableState={tableState}
                onTableStateChange={handleTableStateChange}
              />
            </div>
          </Card>
        </section>
      </main>
    </div>
  );
}
