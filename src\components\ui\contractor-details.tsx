import {
  AlertCircle,
  Building2,
  Calendar,
  CheckCircle,
  Mail,
  Phone,
  Users,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useState } from 'react';
import type { ContractorProfileData } from '../../hooks/use-contractor-profile';
import { Badge } from './badge';
import { Card, CardContent, CardHeader, CardTitle } from './card';
import { FileUploadModal, type FileUploadResult } from './file-upload-modal';

interface ContractorDetailsProps {
  contractorData: ContractorProfileData;
  className?: string;
}

/**
 * Component to display contractor-specific details including company info and user details
 */
export function ContractorDetails({
  contractorData,
  className,
}: ContractorDetailsProps) {
  const { user, contractor } = contractorData;
  const t = useTranslations('profilePage.contractorDetails');

  // State for certificate upload modals
  const [isRegistrationUploadOpen, setIsRegistrationUploadOpen] =
    useState(false);
  const [isLifUploadOpen, setIsLifUploadOpen] = useState(false);
  const [isUploading, setIsUploading] = useState(false);

  // Handle certificate upload
  const handleCertificateUpload = async (
    results: FileUploadResult[],
    type: 'registration' | 'lif',
  ) => {
    setIsUploading(true);
    try {
      // TODO: Implement actual upload logic here
      // The files are already uploaded, we have the URLs in results
      console.log(`Uploaded ${type} certificates:`, results);

      // Simulate processing delay
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // TODO: Save the uploaded URLs to the database or update contractor data
      // You might want to call your API to save the file URLs
      // results.forEach(result => {
      //   console.log(`File: ${result.file.name}, URL: ${result.url}`);
      // });

      // TODO: Refresh contractor data after successful upload
      // You might want to call a refresh function or trigger a re-fetch
    } catch (error) {
      console.error('Upload processing failed:', error);
      throw error;
    } finally {
      setIsUploading(false);
    }
  };

  if (!user || !contractor) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <AlertCircle className="h-5 w-5 text-amber-600" />
            <span>{t('incomplete.title')}</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">
            {t('incomplete.description')}
          </p>
        </CardContent>
      </Card>
    );
  }

  const formatDate = (dateString: string | null) => {
    if (!dateString) return t('companyInformation.notSpecified');
    return new Date(dateString).toLocaleDateString('en-MY', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const getCompanyTypeDisplay = (type: string) => {
    switch (type) {
      case 'COMPETENT_FIRM':
        return t('companyInformation.types.competentFirm');
      case 'NON_COMPETENT_FIRM':
        return t('companyInformation.types.nonCompetentFirm');
      case 'OEM':
        return t('companyInformation.types.oem');
      default:
        return type;
    }
  };

  return (
    <div className={`space-y-6 ${className || ''}`}>
      {/* Company Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Building2 className="h-5 w-5 text-blue-600" />
            <span>{t('companyInformation.title')}</span>
            {contractor.is_active && (
              <Badge variant="default" className="ml-2">
                <CheckCircle className="h-3 w-3 mr-1" />
                {t('companyInformation.active')}
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <div>
                <h4 className="font-semibold text-sm text-muted-foreground mb-1">
                  {t('companyInformation.companyName')}
                </h4>
                <p className="font-medium">{contractor.name}</p>
              </div>

              <div>
                <h4 className="font-semibold text-sm text-muted-foreground mb-1">
                  {t('companyInformation.companyType')}
                </h4>
                <Badge variant="secondary">
                  {getCompanyTypeDisplay(contractor.contractor_type)}
                </Badge>
              </div>

              {contractor.code && (
                <div>
                  <h4 className="font-semibold text-sm text-muted-foreground mb-1">
                    {t('companyInformation.companyCode')}
                  </h4>
                  <p className="font-mono text-sm">{contractor.code}</p>
                </div>
              )}
            </div>

            <div className="space-y-3">
              {contractor.hotline && (
                <div>
                  <h4 className="font-semibold text-sm text-muted-foreground mb-1">
                    {t('companyInformation.companyHotline')}
                  </h4>
                  <div className="flex items-center space-x-2">
                    <Phone className="h-4 w-4 text-green-600" />
                    <span>{contractor.hotline}</span>
                  </div>
                </div>
              )}

              {contractor.oem_name && (
                <div>
                  <h4 className="font-semibold text-sm text-muted-foreground mb-1">
                    {t('companyInformation.oemName')}
                  </h4>
                  <p>{contractor.oem_name}</p>
                </div>
              )}

              <div>
                <h4 className="font-semibold text-sm text-muted-foreground mb-1">
                  {t('companyInformation.registered')}
                </h4>
                <div className="flex items-center space-x-2">
                  <Calendar className="h-4 w-4 text-blue-600" />
                  <span className="text-sm">
                    {formatDate(contractor.created_at)}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* User Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Users className="h-5 w-5 text-green-600" />
            <span>{t('userDetails.title')}</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <div>
                <h4 className="font-semibold text-sm text-muted-foreground mb-1">
                  {t('userDetails.fullName')}
                </h4>
                <p className="font-medium">{user.name}</p>
              </div>

              <div>
                <h4 className="font-semibold text-sm text-muted-foreground mb-1">
                  {t('userDetails.email')}
                </h4>
                <div className="flex items-center space-x-2">
                  <Mail className="h-4 w-4 text-blue-600" />
                  <span>{user.email}</span>
                </div>
              </div>

              {user.phone_number && (
                <div>
                  <h4 className="font-semibold text-sm text-muted-foreground mb-1">
                    {t('userDetails.phoneNumber')}
                  </h4>
                  <div className="flex items-center space-x-2">
                    <Phone className="h-4 w-4 text-green-600" />
                    <span>{user.phone_number}</span>
                  </div>
                </div>
              )}
            </div>

            <div className="space-y-3">
              <div>
                <h4 className="font-semibold text-sm text-muted-foreground mb-1">
                  {t('userDetails.userType')}
                </h4>
                <Badge variant="secondary">{user.user_role}</Badge>
              </div>

              <div>
                <h4 className="font-semibold text-sm text-muted-foreground mb-1">
                  {t('userDetails.joined')}
                </h4>
                <div className="flex items-center space-x-2">
                  <Calendar className="h-4 w-4 text-green-600" />
                  <span className="text-sm">{formatDate(user.created_at)}</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Documents Section - Placeholder for future file upload functionality */}
      {/* <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FileText className="h-5 w-5 text-purple-600" />
            <span>Documents</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-center py-8">
            <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground mb-4">
              Document management functionality will be available soon.
            </p>
            <div className="flex flex-col sm:flex-row gap-2 justify-center">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsRegistrationUploadOpen(true)}
                className="flex items-center space-x-2"
              >
                <Upload className="h-4 w-4" />
                <span>Upload Registration Certificate</span>
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsLifUploadOpen(true)}
                className="flex items-center space-x-2"
              >
                <Upload className="h-4 w-4" />
                <span>Upload LIF List Files</span>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card> */}

      {/* Certificate Upload Modals */}
      <FileUploadModal
        isOpen={isRegistrationUploadOpen}
        onClose={() => setIsRegistrationUploadOpen(false)}
        onUpload={(results: FileUploadResult[]) =>
          handleCertificateUpload(results, 'registration')
        }
        title={t('documents.registrationModal.title')}
        description={t('documents.registrationModal.description')}
        maxFiles={5}
        acceptedTypes=".pdf,.jpg,.jpeg,.png"
        maxSize={10 * 1024 * 1024}
        isLoading={isUploading}
        folderPath="contractor/registration-certificates"
      />

      <FileUploadModal
        isOpen={isLifUploadOpen}
        onClose={() => setIsLifUploadOpen(false)}
        onUpload={(results: FileUploadResult[]) =>
          handleCertificateUpload(results, 'lif')
        }
        title={t('documents.lifModal.title')}
        description={t('documents.lifModal.description')}
        maxFiles={5}
        acceptedTypes=".pdf,.jpg,.jpeg,.png"
        maxSize={10 * 1024 * 1024}
        isLoading={isUploading}
        folderPath="contractor/lif-lists"
      />
    </div>
  );
}
