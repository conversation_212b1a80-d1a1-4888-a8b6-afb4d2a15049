'use client';

import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  CheckCircle,
  Clock,
  Download,
  Edit,
  Eye,
  FileText,
  Filter,
  Plus,
  Search,
  Trash2,
  Upload,
  XCircle,
} from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';
import { useState } from 'react';

// Mock data for the dashboard - replace with actual data fetching
const mockPmaEntries = [
  {
    id: 'PMA-2024-001',
    company: 'Tech Solutions Sdn Bhd',
    ministry: 'Ministry of Digital',
    location: 'Tower A, Level 12',
    status: 'Approved',
    officer: '<PERSON>',
    date: '19/12/2024',
  },
  {
    id: 'PMA-2024-002',
    company: 'Green Energy Corp',
    ministry: 'Ministry of Energy',
    location: 'Tower B, Level 8',
    status: 'Pending',
    officer: '<PERSON>',
    date: '20/12/2024',
  },
  {
    id: 'PMA-2024-003',
    company: 'Healthcare Plus',
    ministry: 'Ministry of Health',
    location: 'Tower C, Level 5',
    status: 'In Progress',
    officer: 'Sarah Abdullah',
    date: '18/12/2024',
  },
  {
    id: 'PMA-2024-004',
    company: 'Smart Systems Ltd',
    ministry: 'Ministry of Transport',
    location: 'Tower A, Level 3',
    status: 'Rejected',
    officer: 'Rajesh Kumar',
    date: '22/12/2024',
  },
  {
    id: 'PMA-2024-005',
    company: 'Innovation Hub',
    ministry: 'Ministry of Innovation',
    location: 'Tower D, Level 15',
    status: 'Completed',
    officer: 'Maria Santos',
    date: '25/12/2024',
  },
];

const getStatusBadge = (status: string) => {
  switch (status) {
    case 'Approved':
      return (
        <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
          Approved
        </Badge>
      );
    case 'Pending':
      return (
        <Badge className="bg-orange-100 text-orange-800 hover:bg-orange-100">
          Pending
        </Badge>
      );
    case 'In Progress':
      return (
        <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">
          In Progress
        </Badge>
      );
    case 'Rejected':
      return (
        <Badge className="bg-red-100 text-red-800 hover:bg-red-100">
          Rejected
        </Badge>
      );
    case 'Completed':
      return (
        <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
          Completed
        </Badge>
      );
    default:
      return <Badge variant="secondary">{status}</Badge>;
  }
};

export function PmaManagementPage() {
  const router = useRouter();
  const params = useParams();
  const locale = (params?.locale as string) || 'en';
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredEntries, setFilteredEntries] = useState(mockPmaEntries);

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    if (query.trim() === '') {
      setFilteredEntries(mockPmaEntries);
    } else {
      const filtered = mockPmaEntries.filter(
        (entry) =>
          entry.id.toLowerCase().includes(query.toLowerCase()) ||
          entry.company.toLowerCase().includes(query.toLowerCase()) ||
          entry.ministry.toLowerCase().includes(query.toLowerCase()) ||
          entry.officer.toLowerCase().includes(query.toLowerCase()),
      );
      setFilteredEntries(filtered);
    }
  };
  const handleAddNewPma = () => {
    router.push(`/${locale}/pmas/add`);
  };

  const handleViewPma = (id: string) => {
    // Navigate to view/details page
    router.push(`/${locale}/pmas/${id}`);
  };

  const handleEditPma = (id: string) => {
    // Navigate to edit page
    router.push(`/${locale}/pmas/${id}/edit`);
  };

  const handleDeletePma = (id: string) => {
    // Show confirmation dialog and delete
    if (confirm('Are you sure you want to delete this PMA entry?')) {
      // Implement delete logic
      console.log('Deleting PMA:', id);
    }
  };

  const stats = {
    total: 24,
    pending: 7,
    completed: 15,
    rejected: 2,
  };

  return (
    <div className="min-h-screen bg-gray-50/30">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="p-3 rounded-xl bg-primary/10">
              <FileText className="h-8 w-8 text-primary" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                Manage PMA Dashboard
              </h1>
              <p className="text-sm text-gray-600 mt-1">
                Overview and management of all PMA entries
              </p>
            </div>
          </div>{' '}
          <div className="flex items-center gap-3">
            <Button variant="outline" className="gap-2">
              <Download className="h-4 w-4" />
              Export Data
            </Button>
            <Button
              className="gap-2 bg-blue-600 hover:bg-blue-700"
              onClick={handleAddNewPma}
            >
              <Plus className="h-4 w-4" />
              Add New PMA
            </Button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        <div className="max-w-7xl mx-auto space-y-6">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">
                  Total PMA Entries
                </CardTitle>
                <FileText className="h-4 w-4 text-blue-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-900">
                  {stats.total}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">
                  Pending Approval
                </CardTitle>
                <Clock className="h-4 w-4 text-orange-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-900">
                  {stats.pending}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">
                  Completed
                </CardTitle>
                <CheckCircle className="h-4 w-4 text-green-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-900">
                  {stats.completed}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">
                  Rejected
                </CardTitle>
                <XCircle className="h-4 w-4 text-red-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-900">
                  {stats.rejected}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* PMA Entries Overview */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-lg font-semibold">
                    PMA Entries Overview
                  </CardTitle>
                  <p className="text-sm text-gray-600">
                    Manage and track all PMA assignments
                  </p>
                </div>
                <div className="flex items-center gap-3">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search PMA..."
                      value={searchQuery}
                      onChange={(e) => handleSearch(e.target.value)}
                      className="pl-10 w-64"
                    />
                  </div>
                  <Button variant="outline" size="sm" className="gap-2">
                    <Filter className="h-4 w-4" />
                    Filters
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>PMA No.</TableHead>
                      <TableHead>Pelanggan/Agensi</TableHead>
                      <TableHead>Lokasi</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Pegawai Pengkaji</TableHead>
                      <TableHead>Tarikh Tamat</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredEntries.map((entry) => (
                      <TableRow key={entry.id}>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <FileText className="h-4 w-4 text-blue-600" />
                            <span className="font-medium">{entry.id}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">{entry.company}</div>
                            <div className="text-sm text-gray-600">
                              {entry.ministry}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>{entry.location}</TableCell>
                        <TableCell>{getStatusBadge(entry.status)}</TableCell>
                        <TableCell>{entry.officer}</TableCell>
                        <TableCell>{entry.date}</TableCell>{' '}
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0"
                              onClick={() => handleViewPma(entry.id)}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0"
                              onClick={() => handleEditPma(entry.id)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                              onClick={() => handleDeletePma(entry.id)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {/* Pagination */}
              <div className="flex items-center justify-between pt-4">
                <p className="text-sm text-gray-600">Showing 5 of 24 entries</p>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm">
                    Previous
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="bg-blue-600 text-white"
                  >
                    1
                  </Button>
                  <Button variant="outline" size="sm">
                    2
                  </Button>
                  <Button variant="outline" size="sm">
                    3
                  </Button>
                  <Button variant="outline" size="sm">
                    4
                  </Button>
                  <Button variant="outline" size="sm">
                    Next
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="flex items-center justify-between pt-6 border-t border-gray-200">
            <div className="flex gap-3">
              <Button variant="outline" className="gap-2">
                <Upload className="h-4 w-4" />
                Import PMA Data
              </Button>
              <Button variant="outline" className="gap-2">
                <FileText className="h-4 w-4" />
                Generate Report
              </Button>
            </div>

            <div className="flex gap-3">
              <Button variant="outline" className="gap-2">
                <Download className="h-4 w-4" />
                Save Changes
              </Button>
              <Button
                className="gap-2 bg-blue-600 hover:bg-blue-700"
                onClick={handleAddNewPma}
              >
                <Plus className="h-4 w-4" />
                Add New PMA
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
