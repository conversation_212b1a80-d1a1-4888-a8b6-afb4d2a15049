import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useTranslations } from 'next-intl';
import { toast } from 'sonner';
import { inviteUserByEmail } from '../services/inviteUserByEmail';
import type {
  EmailInvitationResult,
  InviteUserByEmailParams,
} from '../types/invitation';

/**
 * Hook for inviting a user by email to join a project
 * Creates an invitation record and sends an email with invitation link
 */
export function useInviteUserByEmail() {
  const t = useTranslations();
  const queryClient = useQueryClient();

  return useMutation<EmailInvitationResult, Error, InviteUserByEmailParams>({
    mutationFn: inviteUserByEmail,
    onSuccess: (data, variables) => {
      if (data.success) {
        toast.success(
          t('pages.members.modal.success.invited', {
            email: variables.email,
          }),
        );

        // Invalidate project members query to refresh the list
        queryClient.invalidateQueries({
          queryKey: ['project-members', variables.projectId],
        });

        // Invalidate project invitations query if it exists
        queryClient.invalidateQueries({
          queryKey: ['project-invitations', variables.projectId],
        });
      } else {
        toast.error(
          t('pages.members.modal.errors.inviteFailed', {
            email: variables.email,
            error: data.error || 'Unknown error',
          }),
        );
      }
    },
    onError: (error, variables) => {
      console.error('Failed to invite user by email:', error);
      toast.error(
        t('pages.members.modal.errors.inviteFailed', {
          email: variables.email,
          error: error.message,
        }),
      );
    },
  });
}
