'use client';

import { ChevronRight, Home } from 'lucide-react';
import Link from 'next/link';

interface ProjectHeaderProps {
  projectName?: string;
  module: string;
}

export function ProjectHeader({ projectName, module }: ProjectHeaderProps) {
  return (
    <header>
      {/* Minimalist Navigation Bar */}
      <div className="border-b border-gray-200 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            {/* Simple Breadcrumbs */}
            <nav
              className="flex items-center space-x-2 text-sm"
              aria-label="Breadcrumb"
            >
              <Link
                href="/dashboard"
                className="text-gray-500 hover:text-gray-700"
              >
                <Home className="h-4 w-4" />
              </Link>
              <ChevronRight className="h-4 w-4 text-gray-300" />
              <Link
                href="/projects"
                className="text-gray-600 hover:text-gray-900"
              >
                Projects
              </Link>
              <ChevronRight className="h-4 w-4 text-gray-300" />
              <span className="text-gray-600 max-w-[200px] truncate">
                {projectName || 'Current Project'}
              </span>
              <ChevronRight className="h-4 w-4 text-gray-300" />
              <span className="text-gray-900 font-medium">{module}</span>
            </nav>
          </div>
        </div>
      </div>
    </header>
  );
}
