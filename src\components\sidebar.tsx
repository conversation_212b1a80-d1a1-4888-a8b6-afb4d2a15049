'use client';

import { usePermissions } from '@/hooks/use-permissions';
import { AdminSidebar } from './admin-sidebar';
import { ContractorSidebar } from './contractor-sidebar';

/**
 * Main sidebar component that selects the appropriate sidebar based on user role
 */
export function AppSidebar() {
  const { isJKR, isContractor, isLoading } = usePermissions();

  // Show loading state while determining user role
  if (isLoading) {
    return (
      <div className="flex h-screen w-64 border-r bg-background">
        <div className="flex flex-col items-center justify-center w-full">
          <div className="animate-pulse">
            <div className="h-8 w-8 bg-muted rounded-lg mb-2"></div>
            <div className="h-4 w-20 bg-muted rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  // Render admin sidebar for JKR/admin users
  if (isJKR) {
    return <AdminSidebar />;
  }

  // Render contractor sidebar for contractor users (includes project context logic)
  if (isContractor) {
    return <ContractorSidebar />;
  }

  // Fallback to contractor sidebar for any other roles
  return <ContractorSidebar />;
}
