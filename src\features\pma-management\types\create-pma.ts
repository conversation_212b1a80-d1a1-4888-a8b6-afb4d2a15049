export type StateCode =
  | 'JH'
  | 'KD'
  | 'KT'
  | 'ML'
  | 'NS'
  | 'PH'
  | 'PN'
  | 'PK'
  | 'PL'
  | 'SB'
  | 'SW'
  | 'SL'
  | 'TR'
  | 'WP'
  | 'LBN'
  | 'PW'
  | 'OTH';

export type PMACertificateStatus = 'valid' | 'validating' | 'invalid';

export interface CreatePMACertificateInput {
  pma_number: string;
  expiry_date: string;
  status: PMACertificateStatus;
  project_id: string;
  location: string;
  state?: StateCode | null;
  competent_person_id?: string | null;
  file_url?: string | null;
}

export type CreatePMACertificateResponse = {
  id: string;
  pma_number: string;
  expiry_date: string;
  status: PMACertificateStatus;
  project_id: string;
  location: string;
  state: StateCode | null;
  competent_person_id: string | null;
  file_url: string | null;
  created_at: string;
};
