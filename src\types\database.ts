export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

export type Database = {
  graphql_public: {
    Tables: {
      [_ in never]: never;
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      graphql: {
        Args: {
          operationName?: string;
          query?: string;
          variables?: Json;
          extensions?: Json;
        };
        Returns: Json;
      };
    };
    Enums: {
      [_ in never]: never;
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
  public: {
    Tables: {
      agencies: {
        Row: {
          created_at: string | null;
          created_by: string | null;
          deleted_at: string | null;
          deleted_by: string | null;
          id: string;
          name: string;
          state: Database['public']['Enums']['state_code'] | null;
          updated_at: string | null;
          updated_by: string | null;
        };
        Insert: {
          created_at?: string | null;
          created_by?: string | null;
          deleted_at?: string | null;
          deleted_by?: string | null;
          id?: string;
          name: string;
          state?: Database['public']['Enums']['state_code'] | null;
          updated_at?: string | null;
          updated_by?: string | null;
        };
        Update: {
          created_at?: string | null;
          created_by?: string | null;
          deleted_at?: string | null;
          deleted_by?: string | null;
          id?: string;
          name?: string;
          state?: Database['public']['Enums']['state_code'] | null;
          updated_at?: string | null;
          updated_by?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'fk_agencies_created_by';
            columns: ['created_by'];
            isOneToOne: false;
            referencedRelation: 'users';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'fk_agencies_deleted_by';
            columns: ['deleted_by'];
            isOneToOne: false;
            referencedRelation: 'users';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'fk_agencies_updated_by';
            columns: ['updated_by'];
            isOneToOne: false;
            referencedRelation: 'users';
            referencedColumns: ['id'];
          },
        ];
      };
      competent_person: {
        Row: {
          address: string | null;
          cert_exp_date: string | null;
          contractor_id: string;
          cp_registeration_cert: string | null;
          cp_registeration_no: string | null;
          cp_type: Database['public']['Enums']['cp_type'];
          created_at: string | null;
          created_by: string | null;
          deleted_at: string | null;
          deleted_by: string | null;
          ic_no: string;
          id: string;
          name: string;
          no_of_pma: number | null;
          phone_no: string | null;
          updated_at: string | null;
          updated_by: string | null;
        };
        Insert: {
          address?: string | null;
          cert_exp_date?: string | null;
          contractor_id: string;
          cp_registeration_cert?: string | null;
          cp_registeration_no?: string | null;
          cp_type: Database['public']['Enums']['cp_type'];
          created_at?: string | null;
          created_by?: string | null;
          deleted_at?: string | null;
          deleted_by?: string | null;
          ic_no: string;
          id?: string;
          name: string;
          no_of_pma?: number | null;
          phone_no?: string | null;
          updated_at?: string | null;
          updated_by?: string | null;
        };
        Update: {
          address?: string | null;
          cert_exp_date?: string | null;
          contractor_id?: string;
          cp_registeration_cert?: string | null;
          cp_registeration_no?: string | null;
          cp_type?: Database['public']['Enums']['cp_type'];
          created_at?: string | null;
          created_by?: string | null;
          deleted_at?: string | null;
          deleted_by?: string | null;
          ic_no?: string;
          id?: string;
          name?: string;
          no_of_pma?: number | null;
          phone_no?: string | null;
          updated_at?: string | null;
          updated_by?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'competent_person_contractor_id_fkey';
            columns: ['contractor_id'];
            isOneToOne: false;
            referencedRelation: 'contractors';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'fk_competent_person_contractor';
            columns: ['contractor_id'];
            isOneToOne: false;
            referencedRelation: 'contractors';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'fk_competent_person_created_by';
            columns: ['created_by'];
            isOneToOne: false;
            referencedRelation: 'users';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'fk_competent_person_deleted_by';
            columns: ['deleted_by'];
            isOneToOne: false;
            referencedRelation: 'users';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'fk_competent_person_updated_by';
            columns: ['updated_by'];
            isOneToOne: false;
            referencedRelation: 'users';
            referencedColumns: ['id'];
          },
        ];
      };
      complaints: {
        Row: {
          actual_completion_date: string | null;
          cause_of_damage: string | null;
          contractor_id: string | null;
          contractor_name: string | null;
          correction_action: string | null;
          created_at: string | null;
          created_by: string | null;
          date: string;
          deleted_at: string | null;
          deleted_by: string | null;
          description: string | null;
          email: string;
          expected_completion_date: string | null;
          follow_up: Database['public']['Enums']['complaint_follow_up'];
          id: string;
          involves_mantrap: boolean | null;
          location: string | null;
          no_pma_lif: string | null;
          number: string;
          pma_id: string | null;
          project_id: string | null;
          proof_of_repair_urls: string[] | null;
          repair_completion_time: string | null;
          repair_cost: number | null;
          status: Database['public']['Enums']['complaint_status'];
          updated_at: string | null;
          updated_by: string | null;
        };
        Insert: {
          actual_completion_date?: string | null;
          cause_of_damage?: string | null;
          contractor_id?: string | null;
          contractor_name?: string | null;
          correction_action?: string | null;
          created_at?: string | null;
          created_by?: string | null;
          date: string;
          deleted_at?: string | null;
          deleted_by?: string | null;
          description?: string | null;
          email: string;
          expected_completion_date?: string | null;
          follow_up?: Database['public']['Enums']['complaint_follow_up'];
          id?: string;
          involves_mantrap?: boolean | null;
          location?: string | null;
          no_pma_lif?: string | null;
          number: string;
          pma_id?: string | null;
          project_id?: string | null;
          proof_of_repair_urls?: string[] | null;
          repair_completion_time?: string | null;
          repair_cost?: number | null;
          status?: Database['public']['Enums']['complaint_status'];
          updated_at?: string | null;
          updated_by?: string | null;
        };
        Update: {
          actual_completion_date?: string | null;
          cause_of_damage?: string | null;
          contractor_id?: string | null;
          contractor_name?: string | null;
          correction_action?: string | null;
          created_at?: string | null;
          created_by?: string | null;
          date?: string;
          deleted_at?: string | null;
          deleted_by?: string | null;
          description?: string | null;
          email?: string;
          expected_completion_date?: string | null;
          follow_up?: Database['public']['Enums']['complaint_follow_up'];
          id?: string;
          involves_mantrap?: boolean | null;
          location?: string | null;
          no_pma_lif?: string | null;
          number?: string;
          pma_id?: string | null;
          project_id?: string | null;
          proof_of_repair_urls?: string[] | null;
          repair_completion_time?: string | null;
          repair_cost?: number | null;
          status?: Database['public']['Enums']['complaint_status'];
          updated_at?: string | null;
          updated_by?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'complaints_contractor_id_fkey';
            columns: ['contractor_id'];
            isOneToOne: false;
            referencedRelation: 'contractors';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'complaints_pma_id_fkey';
            columns: ['pma_id'];
            isOneToOne: false;
            referencedRelation: 'pma_certificates';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'complaints_project_id_fkey';
            columns: ['project_id'];
            isOneToOne: false;
            referencedRelation: 'projects';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'fk_complaints_created_by';
            columns: ['created_by'];
            isOneToOne: false;
            referencedRelation: 'users';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'fk_complaints_deleted_by';
            columns: ['deleted_by'];
            isOneToOne: false;
            referencedRelation: 'users';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'fk_complaints_updated_by';
            columns: ['updated_by'];
            isOneToOne: false;
            referencedRelation: 'users';
            referencedColumns: ['id'];
          },
        ];
      };
      contractors: {
        Row: {
          appointed_oem_competent_firm: string | null;
          code: string | null;
          contractor_type: Database['public']['Enums']['company_type'];
          created_at: string | null;
          created_by: string | null;
          deleted_at: string | null;
          deleted_by: string | null;
          hotline: string | null;
          id: string;
          is_active: boolean;
          name: string;
          oem_name: string | null;
          updated_at: string | null;
          updated_by: string | null;
        };
        Insert: {
          appointed_oem_competent_firm?: string | null;
          code?: string | null;
          contractor_type: Database['public']['Enums']['company_type'];
          created_at?: string | null;
          created_by?: string | null;
          deleted_at?: string | null;
          deleted_by?: string | null;
          hotline?: string | null;
          id?: string;
          is_active?: boolean;
          name: string;
          oem_name?: string | null;
          updated_at?: string | null;
          updated_by?: string | null;
        };
        Update: {
          appointed_oem_competent_firm?: string | null;
          code?: string | null;
          contractor_type?: Database['public']['Enums']['company_type'];
          created_at?: string | null;
          created_by?: string | null;
          deleted_at?: string | null;
          deleted_by?: string | null;
          hotline?: string | null;
          id?: string;
          is_active?: boolean;
          name?: string;
          oem_name?: string | null;
          updated_at?: string | null;
          updated_by?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'fk_contractors_created_by';
            columns: ['created_by'];
            isOneToOne: false;
            referencedRelation: 'users';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'fk_contractors_deleted_by';
            columns: ['deleted_by'];
            isOneToOne: false;
            referencedRelation: 'users';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'fk_contractors_updated_by';
            columns: ['updated_by'];
            isOneToOne: false;
            referencedRelation: 'users';
            referencedColumns: ['id'];
          },
        ];
      };
      maintenance_logs: {
        Row: {
          contractor_id: string | null;
          created_at: string | null;
          created_by: string | null;
          deleted_at: string | null;
          deleted_by: string | null;
          description: string | null;
          id: string;
          log_date: string;
          operation_log_type: string;
          operation_type: Database['public']['Enums']['operation_type_enum'];
          person_in_charge_name: string | null;
          person_in_charge_phone: string | null;
          pma_id: string | null;
          project_id: string | null;
          status: Database['public']['Enums']['maintenance_status'];
          updated_at: string | null;
          updated_by: string | null;
        };
        Insert: {
          contractor_id?: string | null;
          created_at?: string | null;
          created_by?: string | null;
          deleted_at?: string | null;
          deleted_by?: string | null;
          description?: string | null;
          id?: string;
          log_date: string;
          operation_log_type: string;
          operation_type?: Database['public']['Enums']['operation_type_enum'];
          person_in_charge_name?: string | null;
          person_in_charge_phone?: string | null;
          pma_id?: string | null;
          project_id?: string | null;
          status?: Database['public']['Enums']['maintenance_status'];
          updated_at?: string | null;
          updated_by?: string | null;
        };
        Update: {
          contractor_id?: string | null;
          created_at?: string | null;
          created_by?: string | null;
          deleted_at?: string | null;
          deleted_by?: string | null;
          description?: string | null;
          id?: string;
          log_date?: string;
          operation_log_type?: string;
          operation_type?: Database['public']['Enums']['operation_type_enum'];
          person_in_charge_name?: string | null;
          person_in_charge_phone?: string | null;
          pma_id?: string | null;
          project_id?: string | null;
          status?: Database['public']['Enums']['maintenance_status'];
          updated_at?: string | null;
          updated_by?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'fk_maint_logs_created_by';
            columns: ['created_by'];
            isOneToOne: false;
            referencedRelation: 'users';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'fk_maint_logs_deleted_by';
            columns: ['deleted_by'];
            isOneToOne: false;
            referencedRelation: 'users';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'fk_maint_logs_updated_by';
            columns: ['updated_by'];
            isOneToOne: false;
            referencedRelation: 'users';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'maintenance_logs_contractor_id_fkey';
            columns: ['contractor_id'];
            isOneToOne: false;
            referencedRelation: 'contractors';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'maintenance_logs_pma_id_fkey';
            columns: ['pma_id'];
            isOneToOne: false;
            referencedRelation: 'pma_certificates';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'maintenance_logs_project_id_fkey';
            columns: ['project_id'];
            isOneToOne: false;
            referencedRelation: 'projects';
            referencedColumns: ['id'];
          },
        ];
      };
      pma_certificates: {
        Row: {
          competent_person_id: string | null;
          created_at: string | null;
          created_by: string | null;
          deleted_at: string | null;
          deleted_by: string | null;
          expiry_date: string;
          file_url: string | null;
          id: string;
          location: string | null;
          pma_number: string | null;
          project_id: string | null;
          state: Database['public']['Enums']['state_code'] | null;
          status: Database['public']['Enums']['pma_status'];
          total_repair_cost: number | null;
          total_repair_time: unknown | null;
          updated_at: string | null;
          updated_by: string | null;
          user_id: string | null;
        };
        Insert: {
          competent_person_id?: string | null;
          created_at?: string | null;
          created_by?: string | null;
          deleted_at?: string | null;
          deleted_by?: string | null;
          expiry_date: string;
          file_url?: string | null;
          id?: string;
          location?: string | null;
          pma_number?: string | null;
          project_id?: string | null;
          state?: Database['public']['Enums']['state_code'] | null;
          status?: Database['public']['Enums']['pma_status'];
          total_repair_cost?: number | null;
          total_repair_time?: unknown | null;
          updated_at?: string | null;
          updated_by?: string | null;
          user_id?: string | null;
        };
        Update: {
          competent_person_id?: string | null;
          created_at?: string | null;
          created_by?: string | null;
          deleted_at?: string | null;
          deleted_by?: string | null;
          expiry_date?: string;
          file_url?: string | null;
          id?: string;
          location?: string | null;
          pma_number?: string | null;
          project_id?: string | null;
          state?: Database['public']['Enums']['state_code'] | null;
          status?: Database['public']['Enums']['pma_status'];
          total_repair_cost?: number | null;
          total_repair_time?: unknown | null;
          updated_at?: string | null;
          updated_by?: string | null;
          user_id?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'fk_pma_cert_created_by';
            columns: ['created_by'];
            isOneToOne: false;
            referencedRelation: 'users';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'fk_pma_cert_deleted_by';
            columns: ['deleted_by'];
            isOneToOne: false;
            referencedRelation: 'users';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'fk_pma_cert_updated_by';
            columns: ['updated_by'];
            isOneToOne: false;
            referencedRelation: 'users';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'pma_certificates_competent_person_id_fkey';
            columns: ['competent_person_id'];
            isOneToOne: false;
            referencedRelation: 'competent_person';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'pma_certificates_project_id_fkey';
            columns: ['project_id'];
            isOneToOne: false;
            referencedRelation: 'projects';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'pma_certificates_user_id_fkey';
            columns: ['user_id'];
            isOneToOne: false;
            referencedRelation: 'users';
            referencedColumns: ['id'];
          },
        ];
      };
      project_invitations: {
        Row: {
          created_at: string | null;
          expiry_date: string;
          id: string;
          invitee_email: string;
          invitee_user_id: string | null;
          inviter_user_id: string | null;
          project_id: string;
          responded_at: string | null;
          responded_by: string | null;
          role: Database['public']['Enums']['project_role'];
          status: string;
          supabase_user_id: string | null;
          token: string;
        };
        Insert: {
          created_at?: string | null;
          expiry_date: string;
          id?: string;
          invitee_email: string;
          invitee_user_id?: string | null;
          inviter_user_id?: string | null;
          project_id: string;
          responded_at?: string | null;
          responded_by?: string | null;
          role: Database['public']['Enums']['project_role'];
          status?: string;
          supabase_user_id?: string | null;
          token: string;
        };
        Update: {
          created_at?: string | null;
          expiry_date?: string;
          id?: string;
          invitee_email?: string;
          invitee_user_id?: string | null;
          inviter_user_id?: string | null;
          project_id?: string;
          responded_at?: string | null;
          responded_by?: string | null;
          role?: Database['public']['Enums']['project_role'];
          status?: string;
          supabase_user_id?: string | null;
          token?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'fk_proj_inv_inviter';
            columns: ['inviter_user_id'];
            isOneToOne: false;
            referencedRelation: 'users';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'fk_proj_inv_responder';
            columns: ['responded_by'];
            isOneToOne: false;
            referencedRelation: 'users';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'project_invitations_invitee_user_id_fkey';
            columns: ['invitee_user_id'];
            isOneToOne: false;
            referencedRelation: 'users';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'project_invitations_inviter_user_id_fkey';
            columns: ['inviter_user_id'];
            isOneToOne: false;
            referencedRelation: 'users';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'project_invitations_project_id_fkey';
            columns: ['project_id'];
            isOneToOne: false;
            referencedRelation: 'projects';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'project_invitations_responded_by_fkey';
            columns: ['responded_by'];
            isOneToOne: false;
            referencedRelation: 'users';
            referencedColumns: ['id'];
          },
        ];
      };
      project_users: {
        Row: {
          assigned_date: string | null;
          created_at: string | null;
          created_by: string | null;
          deleted_at: string | null;
          deleted_by: string | null;
          id: string;
          is_active: boolean | null;
          project_id: string;
          role: Database['public']['Enums']['project_role'];
          status: Database['public']['Enums']['project_user_status'];
          updated_at: string | null;
          updated_by: string | null;
          user_id: string;
        };
        Insert: {
          assigned_date?: string | null;
          created_at?: string | null;
          created_by?: string | null;
          deleted_at?: string | null;
          deleted_by?: string | null;
          id?: string;
          is_active?: boolean | null;
          project_id: string;
          role: Database['public']['Enums']['project_role'];
          status?: Database['public']['Enums']['project_user_status'];
          updated_at?: string | null;
          updated_by?: string | null;
          user_id: string;
        };
        Update: {
          assigned_date?: string | null;
          created_at?: string | null;
          created_by?: string | null;
          deleted_at?: string | null;
          deleted_by?: string | null;
          id?: string;
          is_active?: boolean | null;
          project_id?: string;
          role?: Database['public']['Enums']['project_role'];
          status?: Database['public']['Enums']['project_user_status'];
          updated_at?: string | null;
          updated_by?: string | null;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: 'fk_project_users_created_by';
            columns: ['created_by'];
            isOneToOne: false;
            referencedRelation: 'users';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'fk_project_users_deleted_by';
            columns: ['deleted_by'];
            isOneToOne: false;
            referencedRelation: 'users';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'fk_project_users_updated_by';
            columns: ['updated_by'];
            isOneToOne: false;
            referencedRelation: 'users';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'project_users_project_id_fkey';
            columns: ['project_id'];
            isOneToOne: false;
            referencedRelation: 'projects';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'project_users_user_id_fkey';
            columns: ['user_id'];
            isOneToOne: false;
            referencedRelation: 'users';
            referencedColumns: ['id'];
          },
        ];
      };
      projects: {
        Row: {
          agency_id: string | null;
          code: string | null;
          contractor_id: string | null;
          created_at: string | null;
          created_by: string | null;
          deleted_at: string | null;
          deleted_by: string | null;
          end_date: string | null;
          id: string;
          location: string | null;
          name: string;
          start_date: string | null;
          state: Database['public']['Enums']['state_code'] | null;
          status: string | null;
          updated_at: string | null;
          updated_by: string | null;
        };
        Insert: {
          agency_id?: string | null;
          code?: string | null;
          contractor_id?: string | null;
          created_at?: string | null;
          created_by?: string | null;
          deleted_at?: string | null;
          deleted_by?: string | null;
          end_date?: string | null;
          id?: string;
          location?: string | null;
          name: string;
          start_date?: string | null;
          state?: Database['public']['Enums']['state_code'] | null;
          status?: string | null;
          updated_at?: string | null;
          updated_by?: string | null;
        };
        Update: {
          agency_id?: string | null;
          code?: string | null;
          contractor_id?: string | null;
          created_at?: string | null;
          created_by?: string | null;
          deleted_at?: string | null;
          deleted_by?: string | null;
          end_date?: string | null;
          id?: string;
          location?: string | null;
          name?: string;
          start_date?: string | null;
          state?: Database['public']['Enums']['state_code'] | null;
          status?: string | null;
          updated_at?: string | null;
          updated_by?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: 'fk_projects_created_by';
            columns: ['created_by'];
            isOneToOne: false;
            referencedRelation: 'users';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'fk_projects_deleted_by';
            columns: ['deleted_by'];
            isOneToOne: false;
            referencedRelation: 'users';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'fk_projects_updated_by';
            columns: ['updated_by'];
            isOneToOne: false;
            referencedRelation: 'users';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'projects_agency_id_fkey';
            columns: ['agency_id'];
            isOneToOne: false;
            referencedRelation: 'agencies';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'projects_contractor_id_fkey';
            columns: ['contractor_id'];
            isOneToOne: false;
            referencedRelation: 'contractors';
            referencedColumns: ['id'];
          },
        ];
      };
      users: {
        Row: {
          admin_access_mode:
            | Database['public']['Enums']['admin_access_mode']
            | null;
          contractor_id: string | null;
          created_at: string | null;
          created_by: string | null;
          deleted_at: string | null;
          deleted_by: string | null;
          email: string;
          id: string;
          monitoring_state: Database['public']['Enums']['state_code'] | null;
          name: string;
          onboarding_completed: boolean;
          phone_number: string | null;
          state: Database['public']['Enums']['state_code'] | null;
          updated_at: string | null;
          updated_by: string | null;
          user_role: Database['public']['Enums']['user_role'];
        };
        Insert: {
          admin_access_mode?:
            | Database['public']['Enums']['admin_access_mode']
            | null;
          contractor_id?: string | null;
          created_at?: string | null;
          created_by?: string | null;
          deleted_at?: string | null;
          deleted_by?: string | null;
          email: string;
          id?: string;
          monitoring_state?: Database['public']['Enums']['state_code'] | null;
          name: string;
          onboarding_completed?: boolean;
          phone_number?: string | null;
          state?: Database['public']['Enums']['state_code'] | null;
          updated_at?: string | null;
          updated_by?: string | null;
          user_role: Database['public']['Enums']['user_role'];
        };
        Update: {
          admin_access_mode?:
            | Database['public']['Enums']['admin_access_mode']
            | null;
          contractor_id?: string | null;
          created_at?: string | null;
          created_by?: string | null;
          deleted_at?: string | null;
          deleted_by?: string | null;
          email?: string;
          id?: string;
          monitoring_state?: Database['public']['Enums']['state_code'] | null;
          name?: string;
          onboarding_completed?: boolean;
          phone_number?: string | null;
          state?: Database['public']['Enums']['state_code'] | null;
          updated_at?: string | null;
          updated_by?: string | null;
          user_role?: Database['public']['Enums']['user_role'];
        };
        Relationships: [
          {
            foreignKeyName: 'fk_users_created_by';
            columns: ['created_by'];
            isOneToOne: false;
            referencedRelation: 'users';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'fk_users_deleted_by';
            columns: ['deleted_by'];
            isOneToOne: false;
            referencedRelation: 'users';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'fk_users_updated_by';
            columns: ['updated_by'];
            isOneToOne: false;
            referencedRelation: 'users';
            referencedColumns: ['id'];
          },
          {
            foreignKeyName: 'users_contractor_id_fkey';
            columns: ['contractor_id'];
            isOneToOne: false;
            referencedRelation: 'contractors';
            referencedColumns: ['id'];
          },
        ];
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      can_user_create_projects: {
        Args: { user_id_param: string };
        Returns: boolean;
      };
      generate_complaint_number: {
        Args: Record<PropertyKey, never>;
        Returns: string;
      };
      grant_contractor_project_permissions: {
        Args: { user_id_param: string };
        Returns: undefined;
      };
    };
    Enums: {
      admin_access_mode: 'state' | 'project';
      company_type: 'COMPETENT_FIRM' | 'NON_COMPETENT_FIRM' | 'OEM';
      complaint_follow_up: 'in_progress' | 'pending_approval' | 'verified';
      complaint_status: 'open' | 'on_hold' | 'closed';
      cp_type: 'CP1' | 'CP2' | 'CP3';
      maintenance_status: 'fully function' | 'partially function' | 'broken';
      operation_type_enum: 'daily logs' | 'second schedule' | 'mantrap';
      pma_status: 'valid' | 'validating' | 'invalid';
      project_role: 'technician' | 'competent_person' | 'admin' | 'viewer';
      project_user_status: 'invited' | 'accepted' | 'declined';
      state_code:
        | 'JH'
        | 'KD'
        | 'KT'
        | 'ML'
        | 'NS'
        | 'PH'
        | 'PN'
        | 'PK'
        | 'PL'
        | 'SB'
        | 'SW'
        | 'SL'
        | 'TR'
        | 'WP'
        | 'LBN'
        | 'PW'
        | 'OTH';
      user_role: 'contractor' | 'admin' | 'viewer';
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
};

type DefaultSchema = Database[Extract<keyof Database, 'public'>];

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema['Tables'] & DefaultSchema['Views'])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions['schema']]['Tables'] &
        Database[DefaultSchemaTableNameOrOptions['schema']]['Views'])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions['schema']]['Tables'] &
      Database[DefaultSchemaTableNameOrOptions['schema']]['Views'])[TableName] extends {
      Row: infer R;
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema['Tables'] &
        DefaultSchema['Views'])
    ? (DefaultSchema['Tables'] &
        DefaultSchema['Views'])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R;
      }
      ? R
      : never
    : never;

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema['Tables']
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions['schema']]['Tables']
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions['schema']]['Tables'][TableName] extends {
      Insert: infer I;
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema['Tables']
    ? DefaultSchema['Tables'][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I;
      }
      ? I
      : never
    : never;

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema['Tables']
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions['schema']]['Tables']
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions['schema']]['Tables'][TableName] extends {
      Update: infer U;
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema['Tables']
    ? DefaultSchema['Tables'][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U;
      }
      ? U
      : never
    : never;

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema['Enums']
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions['schema']]['Enums']
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions['schema']]['Enums'][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema['Enums']
    ? DefaultSchema['Enums'][DefaultSchemaEnumNameOrOptions]
    : never;

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema['CompositeTypes']
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions['schema']]['CompositeTypes']
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions['schema']]['CompositeTypes'][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema['CompositeTypes']
    ? DefaultSchema['CompositeTypes'][PublicCompositeTypeNameOrOptions]
    : never;

export const Constants = {
  graphql_public: {
    Enums: {},
  },
  public: {
    Enums: {
      admin_access_mode: ['state', 'project'],
      company_type: ['COMPETENT_FIRM', 'NON_COMPETENT_FIRM', 'OEM'],
      complaint_follow_up: ['in_progress', 'pending_approval', 'verified'],
      complaint_status: ['open', 'on_hold', 'closed'],
      cp_type: ['CP1', 'CP2', 'CP3'],
      maintenance_status: ['fully function', 'partially function', 'broken'],
      operation_type_enum: ['daily logs', 'second schedule', 'mantrap'],
      pma_status: ['valid', 'validating', 'invalid'],
      project_role: ['technician', 'competent_person', 'admin', 'viewer'],
      project_user_status: ['invited', 'accepted', 'declined'],
      state_code: [
        'JH',
        'KD',
        'KT',
        'ML',
        'NS',
        'PH',
        'PN',
        'PK',
        'PL',
        'SB',
        'SW',
        'SL',
        'TR',
        'WP',
        'LBN',
        'PW',
        'OTH',
      ],
      user_role: ['contractor', 'admin', 'viewer'],
    },
  },
} as const;
