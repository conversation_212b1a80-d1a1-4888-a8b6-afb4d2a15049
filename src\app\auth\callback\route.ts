// app/auth/callback/route.ts
import type { Database } from '@/types/database';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const url = new URL(request.url);
  const code = url.searchParams.get('code');
  const redirectToParam = url.searchParams.get('redirect_to');
  const error = url.searchParams.get('error');

  // Handle auth errors from Supabase
  if (error) {
    const siteUrl = process.env.NEXT_PUBLIC_SITE_URL!;
    return NextResponse.redirect(
      new URL('/auth/error?error=' + encodeURIComponent(error), siteUrl),
    );
  }

  if (!code) {
    const siteUrl = process.env.NEXT_PUBLIC_SITE_URL!;
    return NextResponse.redirect(new URL('/auth/login', siteUrl));
  }

  // Create server client with proper cookie handling for PKCE flow
  const cookieStore = await cookies();

  const supabase = createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll();
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) => {
              cookieStore.set(name, value, options);
            });
          } catch {
            // Cookie setting failed, but this is not critical for the auth flow
          }
        },
      },
    },
  );

  // Exchange authorization code for session
  const { data: sessionResult, error: authError } =
    await supabase.auth.exchangeCodeForSession(code);

  if (authError || !sessionResult) {
    const siteUrl = process.env.NEXT_PUBLIC_SITE_URL!;
    return NextResponse.redirect(
      new URL('/auth/error?error=invalid_code', siteUrl),
    );
  }

  const user = sessionResult.user;

  // Ensure user profile exists in database
  const { data: existingProfile, error: profileErr } = await supabase
    .from('users')
    .select('id,onboarding_completed')
    .eq('id', user.id)
    .single();

  // Create profile if it doesn't exist
  if (profileErr?.code === 'PGRST116') {
    await supabase.from('users').insert({
      id: user.id,
      email: user.email ?? '',
      name: user.user_metadata?.full_name ?? user.email!.split('@')[0],
      phone_number: user.user_metadata?.phone_number ?? null,
      user_role: user.user_metadata?.role ?? 'contractor',
      onboarding_completed: false,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    });
  }

  // Determine final redirect destination
  let finalRedirect = redirectToParam;
  if (!finalRedirect) {
    const role = user.user_metadata?.role ?? 'contractor';
    const onboardOK = !!existingProfile?.onboarding_completed;
    finalRedirect = onboardOK
      ? role === 'admin'
        ? '/admin/dashboard'
        : '/dashboard'
      : '/profile';
  }

  const siteUrl = process.env.NEXT_PUBLIC_SITE_URL!;
  return NextResponse.redirect(new URL(finalRedirect, siteUrl));
}
