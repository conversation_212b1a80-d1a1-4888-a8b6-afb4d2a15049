'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { zodResolver } from '@hookform/resolvers/zod';
import { Building2, Map, ShieldCheck } from 'lucide-react';
import { useForm } from 'react-hook-form';
import {
  ADMIN_ACCESS_MODE_OPTIONS,
  adminOnboardingSchema,
  STATE_OPTIONS,
  type AdminOnboardingFormValues,
} from '../schemas/admin-onboarding-schemas';

interface AdminAccessModeFormProps {
  onSubmit: (values: AdminOnboardingFormValues) => void | Promise<void>;
  defaultValues?: Partial<AdminOnboardingFormValues>;
  isLoading?: boolean;
}

/**
 * Form component for admin access mode and monitoring state selection
 * Collects admin-specific configuration during onboarding
 */
export function AdminAccessModeForm({
  onSubmit,
  defaultValues,
  isLoading = false,
}: AdminAccessModeFormProps) {
  const form = useForm<AdminOnboardingFormValues>({
    resolver: zodResolver(adminOnboardingSchema),
    defaultValues: {
      fullName: '',
      phoneNumber: '',
      adminAccessMode: undefined,
      monitoringState: undefined,
      ...defaultValues,
    },
  });

  const watchedAccessMode = form.watch('adminAccessMode');

  const handleSubmit = async (values: AdminOnboardingFormValues) => {
    try {
      await onSubmit(values);
    } catch (error) {
      console.error('Admin onboarding form submission error:', error);
    }
  };

  return (
    <Card className="w-full max-w-3xl mx-auto">
      <CardHeader className="text-center space-y-4">
        <div className="flex justify-center">
          <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-full">
            <ShieldCheck className="h-8 w-8 text-blue-600 dark:text-blue-400" />
          </div>
        </div>
        <div>
          <CardTitle className="text-2xl font-bold">
            Complete Administrator Setup
          </CardTitle>
          <CardDescription className="text-base mt-2">
            Configure your administrative access and monitoring permissions
          </CardDescription>
        </div>
      </CardHeader>

      <CardContent className="space-y-8">
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-8"
          >
            {/* Personal Information Section */}
            <div className="space-y-6">
              <div className="flex items-center space-x-3 pb-4 border-b">
                <Building2 className="h-5 w-5 text-primary" />
                <h3 className="text-lg font-semibold">Profile Information</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="fullName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Full Name <span className="text-destructive">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input placeholder="Enter your full name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="phoneNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Phone Number</FormLabel>
                      <FormControl>
                        <Input
                          type="tel"
                          placeholder="Enter your phone number"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Optional - for contact purposes
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Access Mode Configuration */}
            <div className="space-y-6">
              <div className="flex items-center space-x-3 pb-4 border-b">
                <ShieldCheck className="h-5 w-5 text-primary" />
                <h3 className="text-lg font-semibold">Administrative Access</h3>
              </div>

              <FormField
                control={form.control}
                name="adminAccessMode"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Access Level <span className="text-destructive">*</span>
                    </FormLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        value={field.value}
                        className="grid grid-cols-1 gap-4"
                      >
                        {ADMIN_ACCESS_MODE_OPTIONS.map((option) => (
                          <div
                            key={option.value}
                            className="flex items-start space-x-3 space-y-0 rounded-lg border p-4 hover:bg-muted/50 transition-colors"
                          >
                            <RadioGroupItem
                              value={option.value}
                              className="mt-1"
                            />
                            <div className="flex-1 space-y-1">
                              <div className="flex items-center space-x-2">
                                {option.value === 'project' ? (
                                  <Building2 className="h-4 w-4 text-blue-600" />
                                ) : (
                                  <Map className="h-4 w-4 text-green-600" />
                                )}
                                <span className="font-medium text-sm">
                                  {option.label}
                                </span>
                              </div>
                              <p className="text-sm text-muted-foreground">
                                {option.description}
                              </p>
                            </div>
                          </div>
                        ))}
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* State Selection - Only show if state access mode is selected */}
              {watchedAccessMode === 'state' && (
                <FormField
                  control={form.control}
                  name="monitoringState"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Monitoring State{' '}
                        <span className="text-destructive">*</span>
                      </FormLabel>
                      <FormControl>
                        <Select
                          onValueChange={field.onChange}
                          value={field.value}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select the state you will monitor" />
                          </SelectTrigger>
                          <SelectContent>
                            {STATE_OPTIONS.map((state) => (
                              <SelectItem key={state.value} value={state.value}>
                                <div className="flex items-center space-x-2">
                                  <Map className="h-4 w-4" />
                                  <span>{state.label}</span>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormDescription>
                        You will only have access to projects within this state
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}
            </div>

            {/* Submit Button */}
            <div className="flex justify-end pt-6 border-t">
              <Button
                type="submit"
                size="lg"
                disabled={isLoading}
                className="min-w-[200px]"
              >
                {isLoading ? 'Completing Setup...' : 'Complete Setup'}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
