import { supabase } from '@/lib/supabase';
import type {
  BatchInvitationResult,
  InvitationResult,
  ProjectRole,
  UserExistenceCheck,
} from '../types/invitation';
import { inviteUserByEmail } from './inviteUserByEmail';

/**
 * Check if a user exists by email in the users table
 */
export async function checkUserExists(
  email: string,
): Promise<UserExistenceCheck> {
  try {
    const { data, error } = await supabase
      .from('users')
      .select('id, name, email')
      .eq('email', email.toLowerCase().trim())
      .maybeSingle();

    if (error) {
      console.error('Error checking user existence:', error);
      return { email, exists: false };
    }

    if (data) {
      return {
        email,
        exists: true,
        userId: data.id,
        name: data.name,
      };
    }

    return { email, exists: false };
  } catch (error) {
    console.error('Error in checkUserExists:', error);
    return { email, exists: false };
  }
}

/**
 * Check if a user is already added to a project
 */
export async function checkUserInProject(
  userId: string,
  projectId: string,
): Promise<boolean> {
  try {
    const { data, error } = await supabase
      .from('project_users')
      .select('id')
      .eq('user_id', userId)
      .eq('project_id', projectId)
      .eq('is_active', true)
      .maybeSingle();

    if (error) {
      console.error('Error checking user in project:', error);
      return false;
    }

    return !!data;
  } catch (error) {
    console.error('Error in checkUserInProject:', error);
    return false;
  }
}

/**
 * Add an existing user to a project
 */
export async function addUserToProject(
  userId: string,
  projectId: string,
  role: ProjectRole,
  inviterId: string,
): Promise<{ success: boolean; error?: string }> {
  try {
    const { error } = await supabase.from('project_users').insert({
      user_id: userId,
      project_id: projectId,
      role,
      assigned_date: new Date().toISOString().split('T')[0], // Current date
      is_active: true,
      created_by: inviterId,
      created_at: new Date().toISOString(),
    });

    if (error) {
      console.error('Error adding user to project:', error);
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (error) {
    console.error('Error in addUserToProject:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Process a single invitation for an existing user
 */
export async function processExistingUserInvitation(
  email: string,
  projectId: string,
  role: ProjectRole,
  inviterId: string,
): Promise<InvitationResult> {
  try {
    // Step 1: Check if user exists
    const userCheck = await checkUserExists(email);

    if (!userCheck.exists || !userCheck.userId) {
      return {
        email,
        status: 'user_not_found',
        message: 'User with this email does not exist in the system.',
      };
    }

    // Step 2: Check if user is already in the project
    const isAlreadyInProject = await checkUserInProject(
      userCheck.userId,
      projectId,
    );

    if (isAlreadyInProject) {
      return {
        email,
        status: 'already_added',
        message: `${userCheck.name || email} is already a member of this project.`,
      };
    }

    // Step 3: Add user to project
    const addResult = await addUserToProject(
      userCheck.userId,
      projectId,
      role,
      inviterId,
    );

    if (!addResult.success) {
      return {
        email,
        status: 'error',
        message: 'Failed to add user to project.',
        error: addResult.error,
      };
    }

    return {
      email,
      status: 'success',
      message: `${userCheck.name || email} has been successfully added to the project.`,
    };
  } catch (error) {
    console.error('Error processing invitation:', error);
    return {
      email,
      status: 'error',
      message: 'An unexpected error occurred while processing the invitation.',
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Process a single invitation - handles both existing users and email invitations
 */
export async function processInvitation(
  email: string,
  projectId: string,
  role: ProjectRole,
  inviterId: string,
): Promise<InvitationResult> {
  try {
    // Step 1: Check if user exists
    const userCheck = await checkUserExists(email);

    if (!userCheck.exists || !userCheck.userId) {
      // User doesn't exist - send email invitation
      const inviteResult = await inviteUserByEmail({
        email,
        role,
        projectId,
        inviterId,
      });

      if (inviteResult.success) {
        return {
          email,
          status: 'invited',
          message:
            'Invitation sent successfully. The user will receive an email to join the project.',
          token: inviteResult.token,
        };
      } else {
        return {
          email,
          status: 'error',
          message: 'Failed to send invitation email.',
          error: inviteResult.error,
        };
      }
    }

    // Step 2: Check if user is already in the project
    const isAlreadyInProject = await checkUserInProject(
      userCheck.userId,
      projectId,
    );

    if (isAlreadyInProject) {
      return {
        email,
        status: 'already_added',
        message: `${userCheck.name || email} is already a member of this project.`,
      };
    }

    // Step 3: Add existing user to project
    const addResult = await addUserToProject(
      userCheck.userId,
      projectId,
      role,
      inviterId,
    );

    if (!addResult.success) {
      return {
        email,
        status: 'error',
        message: 'Failed to add user to project.',
        error: addResult.error,
      };
    }

    return {
      email,
      status: 'success',
      message: `${userCheck.name || email} has been successfully added to the project.`,
    };
  } catch (error) {
    console.error('Error processing invitation:', error);
    return {
      email,
      status: 'error',
      message: 'An unexpected error occurred while processing the invitation.',
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Process multiple invitations in batch
 */
export async function processBatchInvitations(
  emails: string[],
  projectId: string,
  role: ProjectRole,
  inviterId: string,
): Promise<BatchInvitationResult> {
  const results: InvitationResult[] = [];

  // Process each email
  for (const email of emails) {
    if (email.trim()) {
      const result = await processInvitation(
        email.trim(),
        projectId,
        role,
        inviterId,
      );
      results.push(result);
    }
  }

  // Calculate summary statistics
  const successCount = results.filter((r) => r.status === 'success').length;
  const errorCount = results.filter((r) => r.status === 'error').length;
  const alreadyAddedCount = results.filter(
    (r) => r.status === 'already_added',
  ).length;
  const userNotFoundCount = results.filter(
    (r) => r.status === 'user_not_found',
  ).length;
  const invitedCount = results.filter((r) => r.status === 'invited').length;

  return {
    results,
    totalProcessed: results.length,
    successCount,
    errorCount,
    alreadyAddedCount,
    userNotFoundCount,
    invitedCount,
  };
}
