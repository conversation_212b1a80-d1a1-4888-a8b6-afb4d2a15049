'use client';

import { ProjectHeader } from '@/components/project-header';
import { AppSidebar } from '@/components/sidebar';
import { SidebarProvider } from '@/components/ui/sidebar';
import { getCurrentModuleFromPathname } from '@/lib/utils';
import { useProjectContext } from '@/providers/project-context';
import { usePathname } from 'next/navigation';
import React from 'react';

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { selectedProject } = useProjectContext();

  const pathname = usePathname();

  const currentModule: string = React.useMemo(
    () => getCurrentModuleFromPathname(pathname),
    [pathname],
  );

  return (
    <SidebarProvider>
      <AppSidebar />
      <main className="flex-1 overflow-auto">
        {selectedProject && (
          <ProjectHeader
            projectName={selectedProject.name}
            module={currentModule}
          />
        )}
        <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">{children}</div>
      </main>
    </SidebarProvider>
  );
}
