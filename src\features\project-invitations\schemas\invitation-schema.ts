import { z } from 'zod';

// Individual member schema
export const invitationMemberSchema = z.object({
  email: z
    .string()
    .min(1, 'Email is required')
    .email('Please enter a valid email address')
    .toLowerCase()
    .trim(),
  role: z
    .enum(['technician', 'competent_person', 'admin', 'viewer'])
    .default('technician'),
});

// Batch invitation schema
export const batchInvitationSchema = z.object({
  members: z
    .array(invitationMemberSchema)
    .min(1, 'At least one member is required')
    .max(10, 'Maximum 10 members can be invited at once')
    .refine((members) => {
      const emails = members.map((m) => m.email);
      const uniqueEmails = new Set(emails);
      return emails.length === uniqueEmails.size;
    }, 'Duplicate email addresses are not allowed'),
});

export type InvitationMemberSchema = z.infer<typeof invitationMemberSchema>;
export type BatchInvitationSchema = z.infer<typeof batchInvitationSchema>;
