import { z } from 'zod';

// Create a function that takes translation messages
export const createPmaFormSchema = (messages: {
  locationRequired: string;
  pmaNumberRequired: string;
  dateReceivedRequired: string;
  competentPersonRequired: string;
  pmaExpiryRequired: string;
}) =>
  z.object({
    location: z.string().min(1, messages.locationRequired),
    pmaNumber: z.string().min(1, messages.pmaNumberRequired),
    dateReceived: z.string().min(1, messages.dateReceivedRequired),
    competentPersonId: z.string().min(1, messages.competentPersonRequired),
    pmaExpiryDate: z.string().min(1, messages.pmaExpiryRequired),
    pdfFile: z.instanceof(File).optional(),
  });

// Default schema for backward compatibility with all new fields
export const pmaFormSchema = z.object({
  location: z
    .string()
    .min(1, 'LIF location is required')
    .max(500, 'Location must be less than 500 characters'),
  pmaNumber: z
    .string()
    .min(1, 'PMA number is required')
    .max(50, 'PMA number must be less than 50 characters')
    .transform((val) => val.trim())
    .refine((val) => val.length > 0, {
      message: 'PMA number cannot be empty',
    }),
  dateReceived: z
    .string()
    .min(1, 'Date received is required')
    .refine((date) => {
      const selectedDate = new Date(date);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      return selectedDate <= today;
    }, 'Date received cannot be in the future'),
  competentPersonId: z
    .string()
    .min(1, 'Competent person is required')
    .uuid('Invalid competent person ID'),
  pmaExpiryDate: z
    .string()
    .min(1, 'PMA expiry date is required')
    .refine((date) => {
      const selectedDate = new Date(date);
      const today = new Date();
      return selectedDate > today;
    }, 'PMA expiry date must be in the future'),
  pdfFile: z
    .instanceof(File)
    .optional()
    .refine((file) => {
      if (!file) return true;
      return file.size <= 10 * 1024 * 1024; // 10MB
    }, 'File size must be less than 10MB')
    .refine((file) => {
      if (!file) return true;
      return ['application/pdf'].includes(file.type);
    }, 'Only PDF files are allowed'),
});

export type PmaFormSchema = z.infer<typeof pmaFormSchema>;
