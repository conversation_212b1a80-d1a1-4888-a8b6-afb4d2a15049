import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { cn } from '@/lib/utils';
import {
  ArrowDown,
  ArrowUp,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  ChevronsUpDown,
  FileText,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import React from 'react';

// Generic row interface
export interface BaseTableRow {
  id: string | number;
  [key: string]: unknown;
}

// Generic table state interface
export interface BaseTableState {
  columnVisibility?: Record<string, boolean>;
  sorting?: {
    column: string;
    direction: 'asc' | 'desc';
  };
  [key: string]: unknown;
}

/**
 * Generic, reusable table section component.
 *
 * Props:
 * - data: Array of row objects.
 * - columns: Array of column definitions ({ key, label, render? }).
 * - isLoading: Show loading skeleton.
 * - emptyMessage: Message to show when data is empty.
 * - tableState: (optional) Table state object (for sorting, column visibility, etc.).
 * - onTableStateChange: (optional) Callback to update table state.
 * - renderCell: (optional) Custom cell renderer (row, key) => ReactNode.
 * - currentPage: (optional) Current page number.
 * - totalPages: (optional) Total number of pages.
 * - onPageChange: (optional) Callback to change page.
 * - pageSize: (optional) Number of rows per page.
 * - onPageSizeChange: (optional) Callback to change page size.
 * - pageSizeOptions: (optional) Array of available page sizes.
 * - totalRecords: (optional) Total number of records.
 */
export interface TableColumn<T extends BaseTableRow = BaseTableRow> {
  key: keyof T | string;
  label: string;
  className?: string;
  render?: (row: T) => React.ReactNode;
  headerRender?: () => React.ReactNode;
}

export interface TableSectionProps<
  T extends BaseTableRow = BaseTableRow,
  S extends BaseTableState = BaseTableState,
> {
  data: T[];
  columns: TableColumn<T>[];
  isLoading?: boolean;
  emptyMessage?: string;
  tableState?: S & {
    columnVisibility?: Record<string, boolean>;
    sorting?: {
      column: string;
      direction: 'asc' | 'desc';
    };
  };
  onTableStateChange?: (state: S) => void;
  renderCell?: (row: T, key: keyof T | string) => React.ReactNode;
  currentPage?: number;
  totalPages?: number;
  onPageChange?: (page: number) => void;
  pageSize?: number;
  onPageSizeChange?: (size: number) => void;
  pageSizeOptions?: number[];
}

function TableSkeleton<T extends BaseTableRow>({
  columns,
}: {
  columns: TableColumn<T>[];
}) {
  return (
    <div className="space-y-4">
      {/* Header skeleton */}
      <div className="flex items-center space-x-4 p-6 bg-gradient-to-r from-slate-50 to-gray-50 rounded-xl border border-slate-100">
        {columns.map((column) => (
          <Skeleton
            key={String(column.key)}
            className={cn(
              'h-5 bg-gradient-to-r from-slate-200 to-slate-300',
              'w-24',
            )}
          />
        ))}
      </div>
      {/* Row skeletons */}
      {Array.from({ length: 5 }).map((_, i) => (
        <div
          key={i}
          className="flex items-center space-x-4 p-6 bg-white rounded-xl border border-slate-100 hover:shadow-sm transition-all duration-300"
          style={{ animationDelay: `${i * 100}ms` }}
        >
          {columns.map((column) => (
            <Skeleton
              key={String(column.key)}
              className={cn(
                'h-4 bg-gradient-to-r from-slate-100 to-slate-200',
                'w-24',
              )}
            />
          ))}
        </div>
      ))}
    </div>
  );
}

function EmptyState({ message }: { message: string }) {
  const t = useTranslations('common');

  return (
    <Card className="border-0 bg-gradient-to-br from-slate-50 via-white to-blue-50/20 shadow-sm">
      <div className="flex flex-col items-center justify-center py-20 text-center">
        <div className="relative mb-6">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-3xl blur-xl opacity-20 animate-pulse" />
          <div className="relative p-6 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-3xl border border-blue-100/50 shadow-sm">
            <FileText className="h-12 w-12 text-blue-600" />
          </div>
        </div>
        <h3 className="text-2xl font-bold text-gray-800 mb-3 tracking-tight">
          {t('noRecordsFound')}
        </h3>
        <p className="text-base text-gray-600 max-w-md leading-relaxed mb-6">
          {message}
        </p>
        <div className="flex items-center space-x-2 text-xs text-gray-400">
          <div className="w-2 h-2 bg-blue-200 rounded-full animate-pulse" />
          <span>{t('tryAdjustingFilters')}</span>
          <div
            className="w-2 h-2 bg-indigo-200 rounded-full animate-pulse"
            style={{ animationDelay: '0.5s' }}
          />
        </div>
      </div>
    </Card>
  );
}

export function TableSection<
  T extends BaseTableRow = BaseTableRow,
  S extends BaseTableState = BaseTableState,
>({
  data,
  columns,
  isLoading,
  emptyMessage,
  tableState,
  onTableStateChange,
  renderCell,
  currentPage,
  totalPages,
  onPageChange,
  pageSize,
  onPageSizeChange,
  pageSizeOptions,
}: TableSectionProps<T, S>) {
  const t = useTranslations('common');
  const defaultEmptyMessage = emptyMessage || t('noRecordsFound');
  // Column visibility
  const columnVisibility = tableState?.columnVisibility || {};
  const visibleColumns = columns.filter(
    (col) => columnVisibility[col.key as string] !== false,
  );

  // Loading state
  if (isLoading) {
    return (
      <Card className="border-0 bg-gradient-to-br from-white via-slate-50/30 to-blue-50/10 backdrop-blur-sm">
        <div className="p-8">
          <TableSkeleton columns={visibleColumns} />
        </div>
      </Card>
    );
  }

  // Empty state
  if (!data || data.length === 0) {
    return (
      <Card className="border-0 bg-gradient-to-br from-slate-50 via-white to-blue-50/20 shadow-sm flex items-center justify-center">
        <EmptyState message={defaultEmptyMessage} />
      </Card>
    );
  }

  // Sorting helpers
  const sorting = tableState?.sorting;
  const handleHeaderClick = (col: TableColumn<T>) => {
    if (!onTableStateChange || !tableState) return;
    const isSorted = sorting?.column === col.key;
    const direction = isSorted && sorting?.direction === 'asc' ? 'desc' : 'asc';
    onTableStateChange({
      ...tableState,
      sorting: {
        column: String(col.key),
        direction,
      },
    });
  };

  const getSortIcon = (col: TableColumn<T>) => {
    if (!sorting || sorting.column !== col.key) {
      return <ChevronsUpDown className="h-4 w-4 text-muted-foreground" />;
    }
    return sorting.direction === 'asc' ? (
      <ArrowUp className="h-4 w-4" />
    ) : (
      <ArrowDown className="h-4 w-4" />
    );
  };

  return (
    <Card className="border-0 bg-gradient-to-br from-white via-slate-50/30 to-blue-50/10 backdrop-blur-sm overflow-hidden">
      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow className="border-b border-slate-200/60 bg-gradient-to-r from-slate-50 via-white to-slate-50 hover:from-slate-100 hover:to-slate-100 transition-all duration-300">
              {visibleColumns.map((column) => (
                <TableHead
                  key={String(column.key)}
                  className={cn(
                    'font-bold text-slate-700 h-14 px-8 text-sm tracking-wide',
                    column.className,
                    'cursor-pointer hover:bg-slate-100/50 transition-all duration-200 group',
                  )}
                  onClick={() => handleHeaderClick(column)}
                >
                  <div className="flex items-center space-x-3">
                    {column.headerRender ? (
                      column.headerRender()
                    ) : (
                      <span className="group-hover:text-slate-900 transition-colors">
                        {column.label}
                      </span>
                    )}
                    {getSortIcon(column)}
                  </div>
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.map((row, index) => (
              <TableRow
                key={row.id || index}
                className={cn(
                  'border-b border-slate-100/60 hover:bg-gradient-to-r hover:from-blue-50/30 hover:to-indigo-50/20 transition-all duration-300 group',
                  index % 2 === 0
                    ? 'bg-white'
                    : 'bg-gradient-to-r from-slate-50/30 to-white',
                )}
              >
                {visibleColumns.map((column) => (
                  <TableCell
                    key={String(row.id) + '-' + String(column.key)}
                    className={cn(
                      'px-8 py-6 text-slate-700 group-hover:text-slate-900 transition-colors',
                      column.className,
                    )}
                  >
                    {column.render
                      ? column.render(row)
                      : renderCell
                        ? renderCell(row, String(column.key))
                        : String(
                            (row as Record<string, unknown>)[
                              column.key as string
                            ] ?? '',
                          )}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
      {/* Pagination Row (shadcn/ui style) */}
      {typeof currentPage === 'number' &&
        typeof totalPages === 'number' &&
        onPageChange &&
        pageSize &&
        onPageSizeChange && (
          <div className="flex items-center justify-between w-full px-6 py-4 border-t border-gray-100/80 bg-white/80">
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium">Rows per page</span>
              <Select
                value={String(pageSize)}
                onValueChange={(v) => onPageSizeChange(Number(v))}
              >
                <SelectTrigger className="h-8 w-[70px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent side="top">
                  {(pageSizeOptions || [10, 20, 30, 50]).map((size) => (
                    <SelectItem key={size} value={String(size)}>
                      {size}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-center space-x-6 lg:space-x-8">
              <div className="flex w-[100px] items-center justify-center text-sm font-medium">
                Page {currentPage} of {totalPages}
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="icon"
                  className="hidden size-8 lg:flex"
                  onClick={() => onPageChange(1)}
                  disabled={currentPage === 1}
                >
                  <span className="sr-only">Go to first page</span>
                  <ChevronsLeft className="w-4 h-4" />
                </Button>
                <Button
                  variant="outline"
                  size="icon"
                  className="size-8"
                  onClick={() => onPageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                >
                  <span className="sr-only">Go to previous page</span>
                  <ChevronLeft className="w-4 h-4" />
                </Button>
                <Button
                  variant="outline"
                  size="icon"
                  className="size-8"
                  onClick={() => onPageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                >
                  <span className="sr-only">Go to next page</span>
                  <ChevronRight className="w-4 h-4" />
                </Button>
                <Button
                  variant="outline"
                  size="icon"
                  className="hidden size-8 lg:flex"
                  onClick={() => onPageChange(totalPages)}
                  disabled={currentPage === totalPages}
                >
                  <span className="sr-only">Go to last page</span>
                  <ChevronsRight className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        )}
    </Card>
  );
}
