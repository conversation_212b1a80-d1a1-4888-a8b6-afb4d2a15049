'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { AddMembersModal } from '@/features/project-invitations';
import { useProject } from '@/features/projects';
import { useProjectContext } from '@/providers/project-context';
import { Mail, Plus, User, Users } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useState } from 'react';

export default function MembersPage() {
  const { selectedProjectId, isInProjectContext } = useProjectContext();
  const {
    data: project,
    isLoading,
    refetch,
  } = useProject(selectedProjectId || '');
  const t = useTranslations('pages.members');
  const _common = useTranslations('common');

  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleModalSuccess = () => {
    // Refresh project data to show new members
    refetch();
    console.log('Members added successfully');
  };

  // Get active project members
  const projectMembers =
    project?.project_users?.filter((pu) => pu.is_active) || [];

  // Function to get role badge variant
  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case 'admin':
        return 'destructive';
      case 'technician':
        return 'default';
      case 'competent_person':
        return 'secondary';
      case 'viewer':
        return 'outline';
      default:
        return 'outline';
    }
  };

  // Function to get status badge variant
  const getStatusBadgeVariant = (status: string | null | undefined) => {
    // Handle undefined/null status values and ensure valid enum values
    const validStatuses = ['invited', 'accepted', 'declined'];
    const validStatus =
      status && validStatuses.includes(status) ? status : 'accepted';
    switch (validStatus) {
      case 'accepted':
        return 'default';
      case 'invited':
        return 'secondary';
      case 'declined':
        return 'destructive';
      default:
        return 'outline';
    }
  };

  // Function to format role display
  const formatRole = (role: string) => {
    const roleKey = role === 'competent_person' ? 'competentPerson' : role;
    return t(`roles.${roleKey}`, { defaultValue: role });
  };

  // Function to format status display
  const formatStatus = (status: string | null | undefined) => {
    // Handle undefined/null status values and ensure valid enum values
    const validStatuses = ['invited', 'accepted', 'declined'];
    const validStatus =
      status && validStatuses.includes(status) ? status : 'accepted';
    return t(`status.${validStatus}`, { defaultValue: validStatus });
  };

  if (!isInProjectContext) {
    return null;
  }

  if (isLoading) {
    return (
      <div className="container mx-auto py-6">
        <div className="space-y-8">
          {/* Header Skeleton */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="p-3 rounded-xl bg-primary/10">
                <Users className="h-8 w-8 text-primary" />
              </div>
              <div>
                <div className="h-8 w-48 bg-muted rounded animate-pulse mb-2" />
                <div className="h-4 w-64 bg-muted rounded animate-pulse" />
              </div>
            </div>
            <div className="h-10 w-32 bg-muted rounded animate-pulse" />
          </div>

          {/* Content Skeleton */}
          <Card>
            <CardHeader>
              <div className="h-6 w-32 bg-muted rounded animate-pulse mb-2" />
              <div className="h-4 w-64 bg-muted rounded animate-pulse" />
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[1, 2, 3].map((i) => (
                  <div
                    key={i}
                    className="flex items-center space-x-4 p-4 border rounded-lg"
                  >
                    <div className="h-10 w-10 bg-muted rounded-full animate-pulse" />
                    <div className="flex-1">
                      <div className="h-4 w-32 bg-muted rounded animate-pulse mb-2" />
                      <div className="h-3 w-48 bg-muted rounded animate-pulse" />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="p-3 rounded-xl bg-primary/10">
              <Users className="h-8 w-8 text-primary" />
            </div>
            <div>
              <h1 className="text-3xl font-bold tracking-tight text-gray-900">
                {t('title')}
              </h1>
              <p className="text-gray-500 mt-1">
                {project
                  ? `Team members for ${project.name}`
                  : t('description')}
              </p>
            </div>
          </div>

          {/* Add Member Button */}
          <Button
            className="flex items-center gap-2"
            onClick={() => setIsModalOpen(true)}
          >
            <Plus className="h-4 w-4" />
            {t('addMember')}
          </Button>
        </div>

        {/* Content */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>{t('teamMembers')}</CardTitle>
                <CardDescription>{t('description')}</CardDescription>
              </div>
              {projectMembers.length > 0 && (
                <Badge variant="secondary" className="text-sm">
                  {projectMembers.length}{' '}
                  {projectMembers.length === 1 ? 'Member' : 'Members'}
                </Badge>
              )}
            </div>
          </CardHeader>
          <CardContent>
            {projectMembers.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <Users className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <p>{t('noMembers')}</p>
                <p className="text-sm mt-2">{t('noMembersDescription')}</p>
              </div>
            ) : (
              <div className="space-y-4">
                {projectMembers.map((member) => (
                  <div
                    key={member.id}
                    className="flex items-center justify-between p-4 border rounded-lg hover:bg-accent/50 transition-colors"
                  >
                    <div className="flex items-center space-x-4">
                      {/* User Avatar */}
                      <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                        <User className="h-5 w-5 text-primary" />
                      </div>

                      {/* User Info */}
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <h4 className="font-medium text-foreground">
                            {member.user.name}
                          </h4>
                          <Badge variant={getRoleBadgeVariant(member.role)}>
                            {formatRole(member.role)}
                          </Badge>
                          <Badge variant={getStatusBadgeVariant(member.status)}>
                            {formatStatus(member.status)}
                          </Badge>
                        </div>
                        <div className="flex items-center space-x-4 text-sm text-muted-foreground mt-1">
                          <div className="flex items-center space-x-1">
                            <Mail className="h-3 w-3" />
                            <span>{member.user.email}</span>
                          </div>
                          {member.assigned_date && (
                            <span>
                              Joined{' '}
                              {new Date(
                                member.assigned_date,
                              ).toLocaleDateString()}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Actions could go here in the future */}
                    <div className="flex items-center space-x-2">
                      {/* Future: Role change, remove member, etc. */}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Add Members Modal */}
        <AddMembersModal
          open={isModalOpen}
          onOpenChange={setIsModalOpen}
          onSuccess={handleModalSuccess}
        />
      </div>
    </div>
  );
}
