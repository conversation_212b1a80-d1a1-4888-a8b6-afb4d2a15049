import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Generates a unique alphanumeric code for companies
 * Format: XXXX-YYYY-ZZZZ (12 characters in 3 groups of 4, using A-Z and 0-9)
 * This provides 36^12 = ~4.7 x 10^18 possibilities, making collisions extremely rare
 */
export function generateCompanyCode(): string {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';

  // Generate timestamp-based prefix for better uniqueness
  const now = new Date();
  const year = now.getFullYear().toString().slice(-2);
  const month = (now.getMonth() + 1).toString().padStart(2, '0');

  // Generate random suffix
  const generateRandomPart = (length: number): string => {
    let result = '';
    for (let i = 0; i < length; i++) {
      result += characters.charAt(
        Math.floor(Math.random() * characters.length),
      );
    }
    return result;
  };

  // Format: YY + MM + 8 random characters = 12 characters total
  const datePart = year + month;
  const randomPart = generateRandomPart(8);

  // Split into groups of 4 for better readability: YYMM-XXXX-XXXX
  const code = datePart + randomPart;
  return `${code.slice(0, 4)}-${code.slice(4, 8)}-${code.slice(8, 12)}`;
}

/**
 * Extracts the current module name from a Next.js pathname.
 * Example: '/en/(app)/dashboard' => 'dashboard'
 */
export function getCurrentModuleFromPathname(pathname: string): string {
  if (!pathname) return '';
  const parts = pathname.replace(/^\/+|\/+$/g, '').split('/');
  const appIndex = parts.indexOf('(app)');
  if (appIndex !== -1 && parts.length > appIndex + 1) {
    return parts[appIndex + 1];
  }
  return parts[parts.length - 1] || '';
}

/**
 * Format a Postgres interval string (e.g., '{00:16:40}') to a human-readable string (e.g., '16m 40s').
 */
export function formatInterval(interval: string): string {
  if (!interval) return '0s';
  // Remove curly braces if present
  const clean = interval.replace(/[{}]/g, '');
  // Split into [hh, mm, ss]
  const [hh, mm, ss] = clean.split(':').map(Number);
  const parts = [];
  if (hh) parts.push(`${hh}h`);
  if (mm) parts.push(`${mm}m`);
  if (ss) parts.push(`${ss}s`);
  return parts.length ? parts.join(' ') : '0s';
}

export function getCurrency(value: number): string {
  return new Intl.NumberFormat('ms-MY', {
    style: 'currency',
    currency: 'MYR',
  }).format(value);
}
