'use client';

import { Button } from '@/components/ui/button';
import CreateMaintenanceLogForm from '@/features/maintenance-logs/components/CreateMaintenanceLogForm';
import { useCreateMaintenanceLog } from '@/features/maintenance-logs/hooks/useMaintenanceLogMutations';
import type { CreateMaintenanceLogInput } from '@/features/maintenance-logs/schemas/create-maintenance-log';
import { usePMACertificates } from '@/features/pma-management';
import { useProjectDetails } from '@/features/projects/hooks/use-project-details';
import { useUserWithProfile } from '@/hooks/use-auth';
import { useProjectContext } from '@/providers/project-context';
import { Wrench } from 'lucide-react';
import { useTranslations } from 'next-intl';
import dynamic from 'next/dynamic';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
const GlobalError = dynamic(
  () => import('@/components/ui/global-error').then((mod) => mod.GlobalError),
  { loading: () => <div>Loading error UI...</div> },
);

export default function CreateMaintenanceLogPage() {
  const router = useRouter();
  const t = useTranslations('pages.maintenanceLogs.create');
  const { selectedProjectId } = useProjectContext();
  const { mutateAsync: createMaintenanceLog, isPending } =
    useCreateMaintenanceLog();

  const {
    data: pmaCertificatesResult,
    isLoading: _isPMACertificatesLoading,
    error: pmaCertificatesError,
  } = usePMACertificates(selectedProjectId);

  const {
    data: project,
    isLoading: isProjectLoading,
    error: projectError,
  } = useProjectDetails(selectedProjectId || '');

  const { data: user } = useUserWithProfile();

  const handleSubmit = async (data: CreateMaintenanceLogInput) => {
    if (!selectedProjectId || !project) return;

    await createMaintenanceLog({
      project_id: selectedProjectId,
      log_date: data.log_date.toISOString().split('T')[0],
      operation_log_type: data.operation_log_type,
      contractor_id: project.contractor_id,
      pma_id: data.pma_id || null,
      description: data.description,
      created_by: user?.id || null,
    });

    router.push('/maintenance-logs');
  };

  if (isProjectLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex items-center gap-2">
          <div className="animate-spin h-6 w-6 border-2 border-primary border-t-transparent rounded-full" />
          <div className="text-lg">{t('loading')}</div>
        </div>
      </div>
    );
  }

  if (pmaCertificatesError) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <GlobalError
          title={t('errors.failedToLoadPma')}
          message={pmaCertificatesError.message || 'Unknown error'}
        />
      </div>
    );
  }

  if (projectError) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <GlobalError
          title={t('errors.failedToLoadProject')}
          message={t('errors.projectNotFound')}
        />
      </div>
    );
  }

  return (
    <div className="bg-gradient-to-br from-slate-50 via-white to-slate-50/50">
      {/* Enhanced Header with Breadcrumbs */}
      <div className="sticky top-0 z-40 bg-white/80 backdrop-blur-lg border-b border-slate-200/60">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            {/* Breadcrumbs */}
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <Link
                href="/maintenance-logs"
                className="text-muted-foreground hover:text-foreground transition-colors"
              >
                <div className="flex items-center space-x-2">
                  <Wrench className="h-4 w-4" />
                  <span>{t('title')}</span>
                </div>
              </Link>
              <span className="text-muted-foreground">/</span>
              <span className="text-primary font-medium">
                {t('breadcrumb')}
              </span>
            </div>

            {/* Actions */}
            <div className="flex items-center space-x-4">
              <Button
                variant="outline"
                onClick={() => router.push('/maintenance-logs')}
              >
                {t('cancel')}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-6 py-8">
        <div className="max-w-2xl mx-auto">
          {/* Header */}
          <div className="mb-8 text-center">
            <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent">
              {t('title')}
            </h1>
            <p className="mt-2 text-lg text-muted-foreground">
              {t('description')}
            </p>
          </div>

          {/* Form */}
          {project && (
            <CreateMaintenanceLogForm
              onSubmit={handleSubmit}
              projectData={project}
              pmaCertificates={pmaCertificatesResult?.data || []}
              isSubmitting={isPending}
            />
          )}
        </div>
      </div>
    </div>
  );
}
